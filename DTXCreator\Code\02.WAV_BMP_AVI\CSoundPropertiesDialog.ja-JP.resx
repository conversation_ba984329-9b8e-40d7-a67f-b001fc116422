<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="textBoxラベル.ToolTip" xml:space="preserve">
    <value>サウンドファイルに名前を自由に設定できます。</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labelラベル.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 12</value>
  </data>
  <data name="labelラベル.Text" xml:space="preserve">
    <value>ラベル</value>
  </data>
  <data name="labelラベル.ToolTip" xml:space="preserve">
    <value>サウンドファイルに名前を自由に設定できます。</value>
  </data>
  <data name="textBoxファイル.ToolTip" xml:space="preserve">
    <value>サウンドファイルのパス。</value>
  </data>
  <data name="labelファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 12</value>
  </data>
  <data name="labelファイル.Text" xml:space="preserve">
    <value>ファイル名</value>
  </data>
  <data name="labelファイル.ToolTip" xml:space="preserve">
    <value>サウンドファイル名</value>
  </data>
  <data name="label音量.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label音量.Text" xml:space="preserve">
    <value>音量</value>
  </data>
  <data name="label音量.ToolTip" xml:space="preserve">
    <value>サウンドの音量を設定します。</value>
  </data>
  <data name="label位置.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label位置.Text" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="label位置.ToolTip" xml:space="preserve">
    <value>サウンドの位置を設定します。</value>
  </data>
  <data name="button参照.Text" xml:space="preserve">
    <value>参照...
</value>
  </data>
  <data name="button参照.ToolTip" xml:space="preserve">
    <value>サウンドファイルをダイアログから選択します。</value>
  </data>
  <data name="hScrollBar音量.ToolTip" xml:space="preserve">
    <value>サウンドの音量を設定します。</value>
  </data>
  <data name="textBox音量.ToolTip" xml:space="preserve">
    <value>サウンドの音量を設定します。</value>
  </data>
  <data name="textBox位置.ToolTip" xml:space="preserve">
    <value>サウンドの位置を設定します。</value>
  </data>
  <data name="hScrollBar位置.ToolTip" xml:space="preserve">
    <value>サウンドの位置を設定します。</value>
  </data>
  <data name="button背景色.Text" xml:space="preserve">
    <value>背景色</value>
  </data>
  <data name="button背景色.ToolTip" xml:space="preserve">
    <value>WAV行の背景色を設定します。</value>
  </data>
  <data name="button文字色.Text" xml:space="preserve">
    <value>文字色</value>
  </data>
  <data name="button文字色.ToolTip" xml:space="preserve">
    <value>WAV行の文字色を設定します。</value>
  </data>
  <data name="button標準色に戻す.Text" xml:space="preserve">
    <value>標準色に戻す</value>
  </data>
  <data name="button標準色に戻す.ToolTip" xml:space="preserve">
    <value>WAV行の背景色と文字色を標準色に戻します。</value>
  </data>
  <data name="button試聴.Text" xml:space="preserve">
    <value>試聴</value>
  </data>
  <data name="button試聴.ToolTip" xml:space="preserve">
    <value>現在のサウンドファイルを試聴します。</value>
  </data>
  <data name="checkBoxBGM.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 16</value>
  </data>
  <data name="checkBoxBGM.Text" xml:space="preserve">
    <value>このサウンドをBGMとして使用する</value>
  </data>
  <data name="checkBoxBGM.ToolTip" xml:space="preserve">
    <value>このサウンドをBGMとして使用する場合に ON にします。</value>
  </data>
  <data name="buttonOK.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="buttonキャンセル.Text" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="buttonキャンセル.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="textBoxWAV番号.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="labelWAV番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 12</value>
  </data>
  <data name="labelWAV番号.Text" xml:space="preserve">
    <value>WAV番号
</value>
  </data>
  <data name="labelWAV番号.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="label音量無音.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label音量無音.Text" xml:space="preserve">
    <value>無音</value>
  </data>
  <data name="label音量無音.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="label位置左.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 12</value>
  </data>
  <data name="label位置左.Text" xml:space="preserve">
    <value>左</value>
  </data>
  <data name="label位置左.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="labe音量原音.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="labe音量原音.Text" xml:space="preserve">
    <value>原音</value>
  </data>
  <data name="labe音量原音.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="label位置右.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 12</value>
  </data>
  <data name="label位置右.Text" xml:space="preserve">
    <value>右</value>
  </data>
  <data name="label位置右.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="label位置中央.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 142</value>
  </data>
  <data name="label位置中央.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label位置中央.Text" xml:space="preserve">
    <value>中央</value>
  </data>
  <data name="label位置中央.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>サウンドプロパティ</value>
  </data>
  <data name="$this.ToolTip" xml:space="preserve">
    <value />
  </data>
</root>