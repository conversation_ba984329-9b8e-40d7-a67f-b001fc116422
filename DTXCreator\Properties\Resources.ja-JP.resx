﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="strBPM選択ダイアログの説明文" xml:space="preserve">
    <value>BPM値を設定して下さい。</value>
  </data>
  <data name="strDTXファイルではありませんMSG" xml:space="preserve">
    <value>DTXファイルではありません。</value>
  </data>
  <data name="strDTXファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>読み込むDTXファイルの選択</value>
  </data>
  <data name="strDTXファイル選択ダイアログのフィルタ" xml:space="preserve">
    <value>DTXファイル (*.dtx;*.gda;*.g2d;*.bms;*.bme)|*.dtx;*.gda;*.g2d;*.bms;*.bme|すべてのファイル(*.*)|*.*</value>
  </data>
  <data name="strしばらくお待ち下さいMSG" xml:space="preserve">
    <value>しばらくお待ち下さい。</value>
  </data>
  <data name="strエラーダイアログのタイトル" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="strコンフィグ読み込み失敗エラーMSG" xml:space="preserve">
    <value>DTXCreator.config の読み込みに失敗しました。</value>
  </data>
  <data name="strサウンドファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>サウンドファイルの選択</value>
  </data>
  <data name="strサウンドファイル選択ダイアログのフィルタ" xml:space="preserve">
    <value>サウンドファイル(*.wav;*.ogg;*.mp3;*.xa)|*.wav;*.ogg;*.mp3;*.xa|すべてのファイル(*.*)|*.*</value>
  </data>
  <data name="strステージ画像ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>Loadingファイルの選択</value>
  </data>
  <data name="strチップ番号に誤りがありますMSG" xml:space="preserve">
    <value>チップ番号に誤りがあります。</value>
  </data>
  <data name="strデフォルトウィンドウタイトル" xml:space="preserve">
    <value>新規</value>
  </data>
  <data name="strファイルが存在しませんMSG" xml:space="preserve">
    <value>ファイルが存在しません。</value>
  </data>
  <data name="strプレビュー画像ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>選曲画像ファイルの選択</value>
  </data>
  <data name="strプレビュー音ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>プレビュー音ファイルの選択</value>
  </data>
  <data name="strプロセスの起動に失敗しましたMSG" xml:space="preserve">
    <value>プロセスの起動に失敗しました。</value>
  </data>
  <data name="str保存中ですMSG" xml:space="preserve">
    <value>保存中です。</value>
  </data>
  <data name="str個のチップが選択されましたMSG" xml:space="preserve">
    <value> 個のチップが選択されました。</value>
  </data>
  <data name="str個のチップを置換しましたMSG" xml:space="preserve">
    <value> 個のチップを置換しました。</value>
  </data>
  <data name="str初期化中ですMSG" xml:space="preserve">
    <value>初期化中です。</value>
  </data>
  <data name="str動画ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>動画ファイルの選択</value>
  </data>
  <data name="str動画ファイル選択ダイアログのフィルタ" xml:space="preserve">
    <value>動画ファイル(*.avi;*.mp4)|*.avi;*.mp4|すべてのファイル(*.*)|*.*</value>
  </data>
  <data name="str名前を付けて保存ダイアログのタイトル" xml:space="preserve">
    <value>名前を付けて保存</value>
  </data>
  <data name="str名前を付けて保存ダイアログのフィルタ" xml:space="preserve">
    <value>DTXファイル(*.dtx)|*.dtx</value>
  </data>
  <data name="str小節番号に誤りがありますMSG" xml:space="preserve">
    <value>小節番号に誤りがあります。</value>
  </data>
  <data name="str検索結果ダイアログのタイトル" xml:space="preserve">
    <value>検索結果</value>
  </data>
  <data name="str画像ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>画像ファイルの選択</value>
  </data>
  <data name="str画像ファイル選択ダイアログのフィルタ" xml:space="preserve">
    <value>画像ファイル(*.bmp;*.jpg;*.jpeg;*.png)|*.bmp;*.jpg;*.jpeg;*.png|すべてのファイル(*.*)|*.*</value>
  </data>
  <data name="str確認ダイアログのタイトル" xml:space="preserve">
    <value>確認</value>
  </data>
  <data name="str結果画像ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>結果画像ファイルの選択</value>
  </data>
  <data name="str編集中のデータを保存しますかMSG" xml:space="preserve">
    <value>編集中のデータを保存しますか？</value>
  </data>
  <data name="str置換結果ダイアログのタイトル" xml:space="preserve">
    <value>置換結果</value>
  </data>
  <data name="str背景画像ファイル選択ダイアログのタイトル" xml:space="preserve">
    <value>背景画像ファイルの選択</value>
  </data>
  <data name="str該当するチップはありませんでしたMSG" xml:space="preserve">
    <value>該当するチップはありませんでした。</value>
  </data>
  <data name="str読み込み中ですMSG" xml:space="preserve">
    <value>読み込み中です。</value>
  </data>
</root>