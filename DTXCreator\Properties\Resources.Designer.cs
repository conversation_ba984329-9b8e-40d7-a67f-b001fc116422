﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DTXCreator.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DTXCreator.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap BGMのみ再生 {
            get {
                object obj = ResourceManager.GetObject("BGMのみ再生", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NX 1.4.2.
        /// </summary>
        internal static string DTXC_VERSION {
            get {
                return ResourceManager.GetString("DTXC_VERSION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
        /// </summary>
        internal static System.Drawing.Icon dtxcreator {
            get {
                object obj = ResourceManager.GetObject("dtxcreator", resourceCulture);
                return ((System.Drawing.Icon)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Redo {
            get {
                object obj = ResourceManager.GetObject("Redo", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify a BPM value..
        /// </summary>
        internal static string strBPM選択ダイアログの説明文 {
            get {
                return ResourceManager.GetString("strBPM選択ダイアログの説明文", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It is not DTX file..
        /// </summary>
        internal static string strDTXファイルではありませんMSG {
            get {
                return ResourceManager.GetString("strDTXファイルではありませんMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open a DTX file.
        /// </summary>
        internal static string strDTXファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("strDTXファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DTX file (*.dtx;*.gda;*.g2d;*.bms;*.bme)|*.dtx;*.gda;*.g2d;*.bms;*.bme|All files(*.*)|*.*.
        /// </summary>
        internal static string strDTXファイル選択ダイアログのフィルタ {
            get {
                return ResourceManager.GetString("strDTXファイル選択ダイアログのフィルタ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string strエラーダイアログのタイトル {
            get {
                return ResourceManager.GetString("strエラーダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to load DTXCreator.config..
        /// </summary>
        internal static string strコンフィグ読み込み失敗エラーMSG {
            get {
                return ResourceManager.GetString("strコンフィグ読み込み失敗エラーMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a sound file.
        /// </summary>
        internal static string strサウンドファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("strサウンドファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound files(*.wav;*.ogg;*.mp3;*.xa)|*.wav;*.ogg;*.mp3;*.xa|All files(*.*)|*.*.
        /// </summary>
        internal static string strサウンドファイル選択ダイアログのフィルタ {
            get {
                return ResourceManager.GetString("strサウンドファイル選択ダイアログのフィルタ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wait a moment....
        /// </summary>
        internal static string strしばらくお待ち下さいMSG {
            get {
                return ResourceManager.GetString("strしばらくお待ち下さいMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a loading image file.
        /// </summary>
        internal static string strステージ画像ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("strステージ画像ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Illegal chip number .
        /// </summary>
        internal static string strチップ番号に誤りがありますMSG {
            get {
                return ResourceManager.GetString("strチップ番号に誤りがありますMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        internal static string strデフォルトウィンドウタイトル {
            get {
                return ResourceManager.GetString("strデフォルトウィンドウタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specified file is not found..
        /// </summary>
        internal static string strファイルが存在しませんMSG {
            get {
                return ResourceManager.GetString("strファイルが存在しませんMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a preview image file.
        /// </summary>
        internal static string strプレビュー画像ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("strプレビュー画像ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a preview sound file.
        /// </summary>
        internal static string strプレビュー音ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("strプレビュー音ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to boot..
        /// </summary>
        internal static string strプロセスの起動に失敗しましたMSG {
            get {
                return ResourceManager.GetString("strプロセスの起動に失敗しましたMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving....
        /// </summary>
        internal static string str保存中ですMSG {
            get {
                return ResourceManager.GetString("str保存中ですMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  chip(s) are selected..
        /// </summary>
        internal static string str個のチップが選択されましたMSG {
            get {
                return ResourceManager.GetString("str個のチップが選択されましたMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  chip(s) are replaced..
        /// </summary>
        internal static string str個のチップを置換しましたMSG {
            get {
                return ResourceManager.GetString("str個のチップを置換しましたMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Initializing....
        /// </summary>
        internal static string str初期化中ですMSG {
            get {
                return ResourceManager.GetString("str初期化中ですMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an Movie file.
        /// </summary>
        internal static string str動画ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str動画ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Movie files(*.avi;*.mp4)|*.avi;*.mp4|All files(*.*)|*.*.
        /// </summary>
        internal static string str動画ファイル選択ダイアログのフィルタ {
            get {
                return ResourceManager.GetString("str動画ファイル選択ダイアログのフィルタ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save As.
        /// </summary>
        internal static string str名前を付けて保存ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str名前を付けて保存ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DTX files(*.dtx)|*.dtx.
        /// </summary>
        internal static string str名前を付けて保存ダイアログのフィルタ {
            get {
                return ResourceManager.GetString("str名前を付けて保存ダイアログのフィルタ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Illegal bar number..
        /// </summary>
        internal static string str小節番号に誤りがありますMSG {
            get {
                return ResourceManager.GetString("str小節番号に誤りがありますMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find results.
        /// </summary>
        internal static string str検索結果ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str検索結果ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Image File.
        /// </summary>
        internal static string str画像ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str画像ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image files(*.bmp;*.jpg;*.jpeg;*.png)|*.bmp;*.jpg;*.jpeg;*.png|All files(*.*)|*.*.
        /// </summary>
        internal static string str画像ファイル選択ダイアログのフィルタ {
            get {
                return ResourceManager.GetString("str画像ファイル選択ダイアログのフィルタ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        internal static string str確認ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str確認ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Reult Screen image file.
        /// </summary>
        internal static string str結果画像ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str結果画像ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The dtx data has changed.
        ///Do you want to save the changes?.
        /// </summary>
        internal static string str編集中のデータを保存しますかMSG {
            get {
                return ResourceManager.GetString("str編集中のデータを保存しますかMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace result.
        /// </summary>
        internal static string str置換結果ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str置換結果ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select background image file.
        /// </summary>
        internal static string str背景画像ファイル選択ダイアログのタイトル {
            get {
                return ResourceManager.GetString("str背景画像ファイル選択ダイアログのタイトル", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No chips are found in the specified conditions..
        /// </summary>
        internal static string str該当するチップはありませんでしたMSG {
            get {
                return ResourceManager.GetString("str該当するチップはありませんでしたMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Now loading....
        /// </summary>
        internal static string str読み込み中ですMSG {
            get {
                return ResourceManager.GetString("str読み込み中ですMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Undo {
            get {
                object obj = ResourceManager.GetObject("Undo", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap コピー {
            get {
                object obj = ResourceManager.GetObject("コピー", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap スピーカー {
            get {
                object obj = ResourceManager.GetObject("スピーカー", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap ﾁｯﾌﾟﾊﾟﾚｯﾄ {
            get {
                object obj = ResourceManager.GetObject("ﾁｯﾌﾟﾊﾟﾚｯﾄ", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap バージョン情報 {
            get {
                object obj = ResourceManager.GetObject("バージョン情報", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap ヘルプ {
            get {
                object obj = ResourceManager.GetObject("ヘルプ", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap りらちょー {
            get {
                object obj = ResourceManager.GetObject("りらちょー", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 上移動 {
            get {
                object obj = ResourceManager.GetObject("上移動", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 下移動 {
            get {
                object obj = ResourceManager.GetObject("下移動", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 保存 {
            get {
                object obj = ResourceManager.GetObject("保存", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 再生 {
            get {
                object obj = ResourceManager.GetObject("再生", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 再生一時停止 {
            get {
                object obj = ResourceManager.GetObject("再生一時停止", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 再生停止 {
            get {
                object obj = ResourceManager.GetObject("再生停止", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 切り取り {
            get {
                object obj = ResourceManager.GetObject("切り取り", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 削除 {
            get {
                object obj = ResourceManager.GetObject("削除", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 動画ドキュメント {
            get {
                object obj = ResourceManager.GetObject("動画ドキュメント", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 新規作成 {
            get {
                object obj = ResourceManager.GetObject("新規作成", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 最初から再生 {
            get {
                object obj = ResourceManager.GetObject("最初から再生", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 画像ドキュメント {
            get {
                object obj = ResourceManager.GetObject("画像ドキュメント", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 矢印ポインタ {
            get {
                object obj = ResourceManager.GetObject("矢印ポインタ", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 表示形式選択 {
            get {
                object obj = ResourceManager.GetObject("表示形式選択", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 貼り付け {
            get {
                object obj = ResourceManager.GetObject("貼り付け", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 鉛筆 {
            get {
                object obj = ResourceManager.GetObject("鉛筆", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 開く {
            get {
                object obj = ResourceManager.GetObject("開く", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap 音楽ドキュメント {
            get {
                object obj = ResourceManager.GetObject("音楽ドキュメント", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
    }
}
