﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BCD40908-F3E2-4707-BFAA-1DD99DF6357D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>FDK</RootNamespace>
    <AssemblyName>FDK</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <ApplicationIcon>
    </ApplicationIcon>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DocumentationFile>
    </DocumentationFile>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <NoWarn>0219</NoWarn>
    <DefineConstants>TRACE;TEST_CancelEnterCodeInAltEnter2 TEST_Direct3D9Ex_</DefineConstants>
    <Optimize>false</Optimize>
    <ErrorReport>prompt</ErrorReport>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DocumentationFile>
    </DocumentationFile>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <NoWarn>0219</NoWarn>
    <DefineConstants>TRACE;TEST_ENGLISH_ TEST_Direct3D9Ex_</DefineConstants>
    <Optimize>true</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>TRACE;TEST_CancelEnterCodeInAltEnter2 TEST_Direct3D9Ex_</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>0219</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE;TEST_ENGLISH_ TEST_Direct3D9Ex_</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <NoWarn>0219</NoWarn>
    <PlatformTarget>x64</PlatformTarget>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Bass.Net, Version=********, Culture=neutral, PublicKeyToken=b7566c273e6ef480, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Runtime\dll\Bass.Net.dll</HintPath>
    </Reference>
    <Reference Include="DirectShowLib-2005">
      <HintPath>..\Runtime\dll\DirectShowLib-2005.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX">
      <HintPath>..\Runtime\dll\SharpDX.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Animation">
      <HintPath>..\Runtime\dll\SharpDX.Animation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Direct3D9">
      <HintPath>..\Runtime\dll\SharpDX.Direct3D9.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DirectInput">
      <HintPath>..\Runtime\dll\SharpDX.DirectInput.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DirectSound">
      <HintPath>..\Runtime\dll\SharpDX.DirectSound.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DXGI">
      <HintPath>..\Runtime\dll\SharpDX.DXGI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Mathematics">
      <HintPath>..\Runtime\dll\SharpDX.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Code\00.Common\COS.cs" />
    <Compile Include="Code\00.Common\CPowerManagement.cs" />
    <Compile Include="Code\00.Common\CSendMessage.cs" />
    <Compile Include="Code\00.Common\CTimerBase.cs" />
    <Compile Include="Code\00.Common\CFixedIntervalProcessing.cs" />
    <Compile Include="Code\00.Common\CCommon.cs" />
    <Compile Include="Code\01.Framework\Core\GameWindowSize.cs" />
    <Compile Include="Code\01.Framework\Core\Game.cs" />
    <Compile Include="Code\01.Framework\Core\GameClock.cs" />
    <Compile Include="Code\01.Framework\Core\GameTime.cs" />
    <Compile Include="Code\01.Framework\Core\GameWindow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Code\01.Framework\DeviceSettings\ConversionMethods.cs" />
    <Compile Include="Code\01.Framework\DeviceSettings\DeviceSettings.cs" />
    <Compile Include="Code\01.Framework\DeviceSettings\Direct3D9Settings.cs" />
    <Compile Include="Code\01.Framework\Enumeration\Enumeration9.cs" />
    <Compile Include="Code\01.Framework\Properties\Resources.Designer.cs" />
    <Compile Include="Code\01.Framework\Rendering\DeviceCreationException.cs" />
    <Compile Include="Code\01.Framework\Rendering\Direct3D9Manager.cs" />
    <Compile Include="Code\01.Framework\Rendering\Enums.cs" />
    <Compile Include="Code\01.Framework\Rendering\GraphicsDeviceManager.cs" />
    <Compile Include="Code\01.Framework\Rendering\NoCompatibleDevicesException.cs" />
    <Compile Include="Code\01.Framework\Rendering\VertexElementAttribute.cs" />
    <Compile Include="Code\01.Framework\Utilities\Camera.cs" />
    <Compile Include="Code\01.Framework\Utilities\TransformedColoredTexturedVertex.cs" />
    <Compile Include="Code\01.Framework\Utilities\TransformedColoredVertex.cs" />
    <Compile Include="Code\01.Framework\Win32\NativeMethods.cs" />
    <Compile Include="Code\01.Framework\Win32\NativeStructures.cs" />
    <Compile Include="Code\01.Framework\Win32\WindowConstants.cs" />
    <Compile Include="Code\02.Input\CInputJoystick.cs" />
    <Compile Include="Code\02.Input\CInputKeyboard.cs" />
    <Compile Include="Code\02.Input\CInputMIDI.cs" />
    <Compile Include="Code\02.Input\CInputMouse.cs" />
    <Compile Include="Code\02.Input\CInputManager.cs" />
    <Compile Include="Code\02.Input\DeviceConstantConverter.cs" />
    <Compile Include="Code\02.Input\EInputDeviceType.cs" />
    <Compile Include="Code\02.Input\IInputDevice.cs" />
    <Compile Include="Code\02.Input\SlimDX.DirectInput.Key.cs" />
    <Compile Include="Code\02.Input\STInputEvent.cs" />
    <Compile Include="Code\03.Sound\BASSThreadedMixerWrapper.cs" />
    <Compile Include="Code\03.Sound\Cmp3ogg.cs" />
    <Compile Include="Code\03.Sound\CSound.cs" />
    <Compile Include="Code\03.Sound\CSoundDeviceASIO.cs" />
    <Compile Include="Code\03.Sound\CSoundDeviceDirectSound.cs" />
    <Compile Include="Code\03.Sound\CSoundDeviceWASAPI.cs" />
    <Compile Include="Code\03.Sound\CSoundTimer.cs" />
    <Compile Include="Code\03.Sound\Cxa.cs" />
    <Compile Include="Code\03.Sound\ESoundDeviceType.cs" />
    <Compile Include="Code\03.Sound\SoundDecoder.cs" />
    <Compile Include="Code\03.Sound\ISoundDevice.cs" />
    <Compile Include="Code\04.Graphics\BitmapUtil.cs" />
    <Compile Include="Code\04.Graphics\CAero.cs" />
    <Compile Include="Code\04.Graphics\CAvi.cs" />
    <Compile Include="Code\04.Graphics\CAviDS.cs" />
    <Compile Include="Code\04.Graphics\CTaskBar.cs" />
    <Compile Include="Code\04.Graphics\CTextureAutofold.cs" />
    <Compile Include="Code\04.Graphics\VertexFormat\ColoredVertex.cs" />
    <Compile Include="Code\04.Graphics\CTexture.cs" />
    <Compile Include="Code\04.Graphics\CTextureCreateFailedException.cs" />
    <Compile Include="Code\04.Graphics\VertexFormat\PositionColoredTexturedVertex.cs" />
    <Compile Include="Code\04.Graphics\VertexFormat\TexturedVertex.cs" />
    <Compile Include="Code\04.Graphics\VertexFormat\TransformedColoredTexturedVertex.cs" />
    <Compile Include="Code\04.Graphics\VertexFormat\TransformedColoredVertex.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Code\00.Common\CActivity.cs" />
    <Compile Include="Code\00.Common\CCounter.cs" />
    <Compile Include="Code\00.Common\CFPS.cs" />
    <Compile Include="Code\00.Common\CIniFile.cs" />
    <Compile Include="Code\00.Common\CTimer.cs" />
    <Compile Include="Code\00.Common\CTraceLogListener.cs" />
    <Compile Include="Code\00.Common\CWin32.cs" />
    <Compile Include="Code\00.Common\CConversion.cs" />
    <Compile Include="Code\05.DirectShow\CDirectShow.cs" />
    <Compile Include="Code\05.DirectShow\CDStoWAVFileImage.cs" />
    <Compile Include="Code\05.DirectShow\MemoryRenderer.cs" />
    <Compile Include="Code\06.Tempo\CBeatDetect.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Code\01.Framework\Properties\Resources.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Code\01.Framework\Resources\sdx_icon_black.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Libraries\libbjxa\libbjxa.csproj">
      <Project>{41e67409-f5ec-4216-a790-2519a7b22e51}</Project>
      <Name>libbjxa</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="AfterBuild">
    <!--Copy SourceFiles="$(TargetDir)$(TargetFileName)" DestinationFolder="$(SolutionDir)$(OutDir)dll" /-->
    <Copy SourceFiles="$(TargetDir)$(TargetFileName)" DestinationFolder="$(SolutionDir)Runtime\dll" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>