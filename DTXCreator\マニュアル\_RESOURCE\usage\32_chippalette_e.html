<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Language" content="en">
<link rel="stylesheet" href="../dtxc.css" type="text/css">
<title>DTXCreator Reference Manual</title>
</head>

<body>

<p>
<table class="pageHeader">
<tr><th class="pageHeader">Advanced operations</th></tr>
<tr><td class="pageHeader">Chip palet</td></tr>
</table>
<p><b>Chip palette</b> is a kind of &quot;chip picker&quot; made as a floating window.
 It's very useful if you register frequency-used chips to it.
</p>
<p align="center">
<img border="0" src="images/chippalette_e_.png" width="300" height="299"></p>

<h1>Show/Hide chip palet</h1>
<p>Click
<img border="0" src="images/EditInformationHS.png" width="16" height="16" style="margin: 0"> 
in the toolbar or click [View(<u>V</u>)] - [Chip palette(<u>P</u>)] to show/hide the chip palette.
</p>
<blockquote>
	<p>
	<img border="0" src="images/chippalette_onoff_e_.png" width="153" height="73"></p>
</blockquote>

<h1>Chip Registrations to the palette</h1>
<p>Drag&amp;drop the chip what you'd like to register to the chip palette.</p>
<blockquote>
	<p><img border="0" src="images/chippalette_add_e_.png" width="617" height="329"></p>
</blockquote>

<h1>Delete chip from the palette</h1>
<p>1. Select the chip you'd like to delete and do right click to show context menu.
</p>
<p>2. Select [Delete from the palette(<u>D</u>)].</p>
<blockquote>
	<p><img border="0" src="images/chippalette_delete_e_.png" width="318" height="316"></p>
</blockquote>


</body>

</html>