<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML><HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="GENERATOR" content="HelpMachine 2.5.1">
<link rel="stylesheet" href="dtxc.css" type="text/css">
<TITLE>At first</TITLE>
</HEAD><BODY>
<!-- HHDesigner Auto Generated HTML :) -->
<p><table class="pageHeader">
<tr><th class="pageHeader">At first</th></tr>
<tr><td class="pageHeader">At first</td></tr>
</table>
</p>
<h1>What's DTXCreator?</h1>

<p><b>DTXCreator</b> (DTXC) is a GUI editor to make DTX files.<br>
The DTXC has similar GUI like BMS<PERSON>reator or GDACreator.
However the DTXC can edit DTX files efficiently
because it support many DTX-spec-related features.

<p align="center">
<img border="0" src="images/dtxc_e.png"><br>
<span class="image_desc">DTXCreator Overview</span></p>

<h1>Requirements</h1>

<ul>
	<li><b>.NET Framework 2.0 and above</b> must be required.</li>
	<li><b>DTXViewer</b> should be required to playback DTX file from DTXCreator.
		(DTXCreator works well even if you don't have DTXViewer.)</li>
</ul>
<h1>Installing and Uninstalling</h1>

<ul>
	<li>You don't have to install DTXCreator. Just unzip the archive and open DTXCreator.exe to start it.</li>
	<li>Delete all DTXCreator files to uninstall it. DTXCreator doesn't use registry.</li>
</ul>

</BODY></HTML>
