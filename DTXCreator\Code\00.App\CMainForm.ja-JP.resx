<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="buttonRESULTIMAGE参照.ToolTip" xml:space="preserve">
    <value>ファイルをダイアログから選択します。</value>
  </data>
  <data name="buttonBACKGROUND参照.ToolTip" xml:space="preserve">
    <value>ファイルをダイアログから選択します。</value>
  </data>
  <data name="buttonSTAGEFILE参照.ToolTip" xml:space="preserve">
    <value>ファイルをダイアログから選択します。</value>
  </data>
  <data name="buttonPREIMAGE参照.ToolTip" xml:space="preserve">
    <value>ファイルをダイアログから選択します。</value>
  </data>
  <data name="buttonPREVIEW参照.ToolTip" xml:space="preserve">
    <value>ファイルをダイアログから選択します。</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labeRESULTIMAGE.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 311</value>
  </data>
  <data name="labeRESULTIMAGE.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="labeRESULTIMAGE.Text" xml:space="preserve">
    <value>結果画像</value>
  </data>
  <data name="labeRESULTIMAGE.ToolTip" xml:space="preserve">
    <value>リザルト画像を設定します。</value>
  </data>
  <data name="labelBACKGROUND.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 286</value>
  </data>
  <data name="labelBACKGROUND.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="labelBACKGROUND.Text" xml:space="preserve">
    <value>背景画像</value>
  </data>
  <data name="labelBACKGROUND.ToolTip" xml:space="preserve">
    <value>背景画像を設定します。</value>
  </data>
  <data name="labelSTAGEFILE.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 261</value>
  </data>
  <data name="labelSTAGEFILE.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 12</value>
  </data>
  <data name="labelSTAGEFILE.Text" xml:space="preserve">
    <value>Loading画像</value>
  </data>
  <data name="labelSTAGEFILE.ToolTip" xml:space="preserve">
    <value>STAGEFILEを設定します。</value>
  </data>
  <data name="labelPREIMAGE.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="labelPREIMAGE.Text" xml:space="preserve">
    <value>選曲画像</value>
  </data>
  <data name="labelPREIMAGE.ToolTip" xml:space="preserve">
    <value>選曲画像を設定します。</value>
  </data>
  <data name="labelPREVIEW.Location" type="System.Drawing.Point, System.Drawing">
    <value>22, 213</value>
  </data>
  <data name="labelPREVIEW.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 12</value>
  </data>
  <data name="labelPREVIEW.Text" xml:space="preserve">
    <value>Preview音</value>
  </data>
  <data name="labelPREVIEW.ToolTip" xml:space="preserve">
    <value>プレビューサウンドを設定します。</value>
  </data>
  <data name="textBoxRESULTIMAGE.ToolTip" xml:space="preserve">
    <value>リザルト画像を設定します。</value>
  </data>
  <data name="textBoxBACKGROUND.ToolTip" xml:space="preserve">
    <value>背景画像を設定します。</value>
  </data>
  <data name="textBoxSTAGEFILE.ToolTip" xml:space="preserve">
    <value>STAGEFILEを設定します。</value>
  </data>
  <data name="textBoxPREIMAGE.ToolTip" xml:space="preserve">
    <value>選曲画像を設定します。</value>
  </data>
  <data name="textBoxPREVIEW.ToolTip" xml:space="preserve">
    <value>プレビューサウンドを設定します。</value>
  </data>
  <data name="textBoxパネル.ToolTip" xml:space="preserve">
    <value>パネル文を設定します。</value>
  </data>
  <data name="labelパネル.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 185</value>
  </data>
  <data name="labelパネル.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 12</value>
  </data>
  <data name="labelパネル.Text" xml:space="preserve">
    <value>パネル文</value>
  </data>
  <data name="labelパネル.ToolTip" xml:space="preserve">
    <value>パネル文を設定します。</value>
  </data>
  <data name="labelBLEVEL.Location" type="System.Drawing.Point, System.Drawing">
    <value>34, 157</value>
  </data>
  <data name="labelBLEVEL.ToolTip" xml:space="preserve">
    <value>ベースの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="textBoxBLEVEL.ToolTip" xml:space="preserve">
    <value>ベースの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="hScrollBarBLEVEL.ToolTip" xml:space="preserve">
    <value>ベースの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="labelGLEVEL.ToolTip" xml:space="preserve">
    <value>ギターの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="textBoxGLEVEL.ToolTip" xml:space="preserve">
    <value>ギターの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="hScrollBarGLEVEL.ToolTip" xml:space="preserve">
    <value>ギターの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="labelDLEVEL.ToolTip" xml:space="preserve">
    <value>ドラムの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="textBoxDLEVEL.ToolTip" xml:space="preserve">
    <value>ドラムの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="hScrollBarDLEVEL.ToolTip" xml:space="preserve">
    <value>ドラムの難易度を、(易) 1～99 (難) で設定します。999まで設定することもできます。</value>
  </data>
  <data name="labelBPM.ToolTip" xml:space="preserve">
    <value>曲の速さをBPM (Beats / Minute) で設定します。</value>
  </data>
  <data name="labelコメント.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 59</value>
  </data>
  <data name="labelコメント.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 12</value>
  </data>
  <data name="labelコメント.Text" xml:space="preserve">
    <value>コメント</value>
  </data>
  <data name="labelコメント.ToolTip" xml:space="preserve">
    <value>演奏データに対するコメント文を設定します。</value>
  </data>
  <data name="label製作者.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 34</value>
  </data>
  <data name="label製作者.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="label製作者.Text" xml:space="preserve">
    <value>製作者</value>
  </data>
  <data name="label製作者.ToolTip" xml:space="preserve">
    <value>演奏データの製作者名を設定します。</value>
  </data>
  <data name="label曲名.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label曲名.Text" xml:space="preserve">
    <value>曲名</value>
  </data>
  <data name="label曲名.ToolTip" xml:space="preserve">
    <value>演奏データのタイトルを設定します。</value>
  </data>
  <data name="numericUpDownBPM.ToolTip" xml:space="preserve">
    <value>曲の速さをBPM (Beats / Minute) で設定します。</value>
  </data>
  <data name="textBoxコメント.ToolTip" xml:space="preserve">
    <value>演奏データに対するコメント文を設定します。</value>
  </data>
  <data name="textBox製作者.ToolTip" xml:space="preserve">
    <value>演奏データの製作者名を設定します。</value>
  </data>
  <data name="textBox曲名.ToolTip" xml:space="preserve">
    <value>演奏データのタイトルを設定します。</value>
  </data>
  <data name="tabPage基本情報.Text" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="tabPage基本情報.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="columnHeaderWAV_ラベル.Text" xml:space="preserve">
    <value>ラベル</value>
  </data>
  <data name="columnHeaderWAV_ファイル名.Text" xml:space="preserve">
    <value>ファイル</value>
  </data>
  <data name="columnHeaderWAV_音量.Text" xml:space="preserve">
    <value>音量</value>
  </data>
  <data name="columnHeaderWAV_位置.Text" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="listViewWAVリスト.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripButtonWAVリスト上移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ上に移動します。</value>
  </data>
  <data name="toolStripButtonWAVリスト下移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ下に移動します。</value>
  </data>
  <data name="toolStripButtonWAVリストプレビュー再生開始.ToolTipText" xml:space="preserve">
    <value>選択されている行のプレビュー音を再生します。</value>
  </data>
  <data name="toolStripButtonWAVリストプレビュー再生停止.ToolTipText" xml:space="preserve">
    <value>プレビュー音の再生を停止します。</value>
  </data>
  <data name="toolStripButtonWAVリストプレビュースイッチ.ToolTipText" xml:space="preserve">
    <value>行をクリックしたときにプレビュー音を再生します。</value>
  </data>
  <data name="toolStripWAVツールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="tabPageWAV.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="columnHeaderBMP_ラベル.Text" xml:space="preserve">
    <value>ラベル</value>
  </data>
  <data name="columnHeaderBMP_ファイル名.Text" xml:space="preserve">
    <value>ファイル</value>
  </data>
  <data name="listViewBMPリスト.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripButtonBMPリスト上移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ上に移動します。</value>
  </data>
  <data name="toolStripButtonBMPリスト下移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ下に移動します。</value>
  </data>
  <data name="toolStripBMPツールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="tabPageBMP.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="columnHeaderAVI_ラベル.Text" xml:space="preserve">
    <value>ラベル</value>
  </data>
  <data name="columnHeaderAVI_ファイル名.Text" xml:space="preserve">
    <value>ファイル</value>
  </data>
  <data name="listViewAVIリスト.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripButtonAVIリスト上移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ上に移動します。</value>
  </data>
  <data name="toolStripButtonAVIリスト下移動.ToolTipText" xml:space="preserve">
    <value>選択行を１つ下に移動します。</value>
  </data>
  <data name="toolStripAVIツールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="tabPageAVI.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="textBox自由入力欄.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="tabPage自由入力.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="tabControl情報パネル.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="splitContainerタブと譜面を分割.Panel1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="pictureBox譜面パネル.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="hScrollBar譜面用水平スクロールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="splitContainerタブと譜面を分割.Panel2.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="splitContainerタブと譜面を分割.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="statusStripステータスバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripMenuItem新規.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem新規.Text" xml:space="preserve">
    <value>新規(&amp;N)</value>
  </data>
  <data name="toolStripMenuItem開く.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem開く.Text" xml:space="preserve">
    <value>開く(&amp;O)</value>
  </data>
  <data name="toolStripMenuItem上書き保存.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem上書き保存.Text" xml:space="preserve">
    <value>上書き保存(&amp;S)</value>
  </data>
  <data name="toolStripMenuItem名前を付けて保存.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem名前を付けて保存.Text" xml:space="preserve">
    <value>名前を付けて保存(&amp;A)</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 6</value>
  </data>
  <data name="toolStripMenuItem終了.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem終了.Text" xml:space="preserve">
    <value>終了(&amp;X)</value>
  </data>
  <data name="toolStripMenuItemファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 22</value>
  </data>
  <data name="toolStripMenuItemファイル.Text" xml:space="preserve">
    <value>ファイル(&amp;F)</value>
  </data>
  <data name="toolStripMenuItemアンドゥ.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItemアンドゥ.Text" xml:space="preserve">
    <value>元に戻す(&amp;U)</value>
  </data>
  <data name="toolStripMenuItemリドゥ.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItemリドゥ.Text" xml:space="preserve">
    <value>やり直す(&amp;R)</value>
  </data>
  <data name="toolStripSeparator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 6</value>
  </data>
  <data name="toolStripMenuItem切り取り.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem切り取り.Text" xml:space="preserve">
    <value>切り取り(&amp;T)</value>
  </data>
  <data name="toolStripMenuItemコピー.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItemコピー.Text" xml:space="preserve">
    <value>コピー(&amp;C)</value>
  </data>
  <data name="toolStripMenuItem貼り付け.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem貼り付け.Text" xml:space="preserve">
    <value>貼り付け(&amp;P)</value>
  </data>
  <data name="toolStripMenuItem削除.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem削除.Text" xml:space="preserve">
    <value>削除(&amp;D)</value>
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 6</value>
  </data>
  <data name="toolStripMenuItemすべて選択.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItemすべて選択.Text" xml:space="preserve">
    <value>すべて選択(&amp;A)</value>
  </data>
  <data name="toolStripSeparator4.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 6</value>
  </data>
  <data name="toolStripMenuItem選択モード.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem選択モード.Text" xml:space="preserve">
    <value>選択モード(&amp;S)</value>
  </data>
  <data name="toolStripMenuItem編集モード.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem編集モード.Text" xml:space="preserve">
    <value>編集モード(&amp;E)</value>
  </data>
  <data name="toolStripMenuItemモード切替.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItemモード切替.Text" xml:space="preserve">
    <value>モード切替(&amp;M)</value>
  </data>
  <data name="toolStripSeparator5.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 6</value>
  </data>
  <data name="toolStripMenuItem検索.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem検索.Text" xml:space="preserve">
    <value>検索(&amp;F)</value>
  </data>
  <data name="toolStripMenuItem置換.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 22</value>
  </data>
  <data name="toolStripMenuItem置換.Text" xml:space="preserve">
    <value>置換(&amp;Q)</value>
  </data>
  <data name="toolStripMenuItem編集.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 22</value>
  </data>
  <data name="toolStripMenuItem編集.Text" xml:space="preserve">
    <value>編集(&amp;E)</value>
  </data>
  <data name="toolStripMenuItemチップパレット.Size" type="System.Drawing.Size, System.Drawing">
    <value>177, 22</value>
  </data>
  <data name="toolStripMenuItemチップパレット.Text" xml:space="preserve">
    <value>チップパレット(&amp;P)</value>
  </data>
  <data name="toolStripMenuItemガイド間隔4分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔8分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔12分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔16分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔24分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔32分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔48分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔64分.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔フリー.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔フリー.Text" xml:space="preserve">
    <value>フリー</value>
  </data>
  <data name="toolStripSeparator6.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 6</value>
  </data>
  <data name="toolStripMenuItemガイド間隔拡大.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔拡大.Text" xml:space="preserve">
    <value>拡大(&amp;W)</value>
  </data>
  <data name="toolStripMenuItemガイド間隔縮小.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔縮小.Text" xml:space="preserve">
    <value>縮小(&amp;N)</value>
  </data>
  <data name="toolStripMenuItemガイド間隔.Size" type="System.Drawing.Size, System.Drawing">
    <value>177, 22</value>
  </data>
  <data name="toolStripMenuItemガイド間隔.Text" xml:space="preserve">
    <value>ガイド間隔(&amp;G)</value>
  </data>
  <data name="toolStripMenuItem表示.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 22</value>
  </data>
  <data name="toolStripMenuItem表示.Text" xml:space="preserve">
    <value>表示(&amp;V)</value>
  </data>
  <data name="toolStripMenuItem先頭から再生.Size" type="System.Drawing.Size, System.Drawing">
    <value>253, 22</value>
  </data>
  <data name="toolStripMenuItem先頭から再生.Text" xml:space="preserve">
    <value>先頭から再生(&amp;T)</value>
  </data>
  <data name="toolStripMenuItem現在位置から再生.Size" type="System.Drawing.Size, System.Drawing">
    <value>253, 22</value>
  </data>
  <data name="toolStripMenuItem現在位置から再生.Text" xml:space="preserve">
    <value>現在の位置から再生(&amp;P)</value>
  </data>
  <data name="toolStripMenuItem現在位置からBGMのみ再生.Size" type="System.Drawing.Size, System.Drawing">
    <value>253, 22</value>
  </data>
  <data name="toolStripMenuItem現在位置からBGMのみ再生.Text" xml:space="preserve">
    <value>現在の位置からBGMのみ再生(&amp;B)</value>
  </data>
  <data name="toolStripMenuItem再生停止.Size" type="System.Drawing.Size, System.Drawing">
    <value>253, 22</value>
  </data>
  <data name="toolStripMenuItem再生停止.Text" xml:space="preserve">
    <value>再生停止(&amp;S)</value>
  </data>
  <data name="toolStripMenuItem再生.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 22</value>
  </data>
  <data name="toolStripMenuItem再生.Text" xml:space="preserve">
    <value>再生(&amp;P)</value>
  </data>
  <data name="toolStripMenuItemオプション.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 22</value>
  </data>
  <data name="toolStripMenuItemオプション.Text" xml:space="preserve">
    <value>オプション(&amp;O)</value>
  </data>
  <data name="toolStripMenuItemツール.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 22</value>
  </data>
  <data name="toolStripMenuItemツール.Text" xml:space="preserve">
    <value>ツール(&amp;T)</value>
  </data>
  <data name="toolStripMenuItemDTXCreaterマニュアル.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 22</value>
  </data>
  <data name="toolStripMenuItemDTXCreaterマニュアル.Text" xml:space="preserve">
    <value>DTXCreator マニュアル(&amp;M)</value>
  </data>
  <data name="toolStripMenuItemバージョン.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 22</value>
  </data>
  <data name="toolStripMenuItemバージョン.Text" xml:space="preserve">
    <value>バージョン情報(&amp;V)</value>
  </data>
  <data name="toolStripMenuItemヘルプ.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 22</value>
  </data>
  <data name="toolStripMenuItemヘルプ.Text" xml:space="preserve">
    <value>ヘルプ(&amp;H)</value>
  </data>
  <data name="menuStripメニューバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripButton新規作成.ToolTipText" xml:space="preserve">
    <value>新しい譜面を作成します。</value>
  </data>
  <data name="toolStripButton開く.ToolTipText" xml:space="preserve">
    <value>新しいDTXファイルを開きます。</value>
  </data>
  <data name="toolStripButton上書き保存.ToolTipText" xml:space="preserve">
    <value>上書き保存します。</value>
  </data>
  <data name="toolStripButton切り取り.ToolTipText" xml:space="preserve">
    <value>選択されているチップをクリップボードへ切り取ります。</value>
  </data>
  <data name="toolStripButtonコピー.ToolTipText" xml:space="preserve">
    <value>選択されているチップをクリップボードへコピーします。</value>
  </data>
  <data name="toolStripButton貼り付け.ToolTipText" xml:space="preserve">
    <value>クリップボードのチップを譜面に貼り付けます。</value>
  </data>
  <data name="toolStripButton削除.ToolTipText" xml:space="preserve">
    <value>選択されているチップを削除します。</value>
  </data>
  <data name="toolStripButtonアンドゥ.ToolTipText" xml:space="preserve">
    <value>作業を１つ元に戻します。</value>
  </data>
  <data name="toolStripButtonリドゥ.ToolTipText" xml:space="preserve">
    <value>作業を１つやり直します。</value>
  </data>
  <data name="toolStripButtonチップパレット.ToolTipText" xml:space="preserve">
    <value>チップパレットを表示します。</value>
  </data>
  <data name="toolStripComboBox譜面拡大率.ToolTipText" xml:space="preserve">
    <value>譜面の縦方向の表示倍率を指定します。</value>
  </data>
  <data name="toolStripComboBoxガイド間隔.ToolTipText" xml:space="preserve">
    <value>ガイド線の間隔を指定します。</value>
  </data>
  <data name="toolStripButton選択モード.ToolTipText" xml:space="preserve">
    <value>選択モードに切り替えます。</value>
  </data>
  <data name="toolStripButton編集モード.ToolTipText" xml:space="preserve">
    <value>編集モードに切り替えます。</value>
  </data>
  <data name="toolStripButton先頭から再生.ToolTipText" xml:space="preserve">
    <value>DTXViewerで先頭から再生します。</value>
  </data>
  <data name="toolStripButton現在位置から再生.ToolTipText" xml:space="preserve">
    <value>DTXViewerで現在の位置から再生します。</value>
  </data>
  <data name="toolStripButton現在位置からBGMのみ再生.ToolTipText" xml:space="preserve">
    <value>DTXViewerで現在の位置からBGMのみ再生します。</value>
  </data>
  <data name="toolStripButton再生停止.ToolTipText" xml:space="preserve">
    <value>DTXViewerでの再生を停止します。</value>
  </data>
  <data name="toolStripComboBox演奏速度.ToolTipText" xml:space="preserve">
    <value>DTXViewerでの再生速度を指定します。 (#DTXVPLAYSPEED)</value>
  </data>
  <data name="toolStripツールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="vScrollBar譜面用垂直スクロールバー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripMenuItem選択チップの切り取り.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem選択チップの切り取り.Text" xml:space="preserve">
    <value>選択チップの切り取り(&amp;T)</value>
  </data>
  <data name="toolStripMenuItem選択チップのコピー.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem選択チップのコピー.Text" xml:space="preserve">
    <value>選択チップのコピー(&amp;C)</value>
  </data>
  <data name="toolStripMenuItem選択チップの貼り付け.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem選択チップの貼り付け.Text" xml:space="preserve">
    <value>選択チップの貼り付け(&amp;P)</value>
  </data>
  <data name="toolStripMenuItem選択チップの削除.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem選択チップの削除.Text" xml:space="preserve">
    <value>選択チップの削除(&amp;D)</value>
  </data>
  <data name="toolStripSeparator15.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 6</value>
  </data>
  <data name="toolStripMenuItemすべてのチップの選択.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItemすべてのチップの選択.Text" xml:space="preserve">
    <value>すべてのチップの選択(&amp;A)</value>
  </data>
  <data name="toolStripMenuItemレーン内のすべてのチップの選択.Size" type="System.Drawing.Size, System.Drawing">
    <value>287, 22</value>
  </data>
  <data name="toolStripMenuItemレーン内のすべてのチップの選択.Text" xml:space="preserve">
    <value>レーン内のすべてのチップの選択(&amp;L) []</value>
  </data>
  <data name="toolStripMenuItem小節内のすべてのチップの選択.Size" type="System.Drawing.Size, System.Drawing">
    <value>287, 22</value>
  </data>
  <data name="toolStripMenuItem小節内のすべてのチップの選択.Text" xml:space="preserve">
    <value>小節内のすべてのチップの選択(&amp;R) []</value>
  </data>
  <data name="toolStripSeparator16.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 6</value>
  </data>
  <data name="toolStripMenuItem小節長変更.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem小節長変更.Text" xml:space="preserve">
    <value>小節長の変更(&amp;B)</value>
  </data>
  <data name="toolStripSeparator17.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 6</value>
  </data>
  <data name="toolStripMenuItem小節の挿入.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem小節の挿入.Text" xml:space="preserve">
    <value>小節の挿入(&amp;I)</value>
  </data>
  <data name="toolStripMenuItem小節の削除.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 22</value>
  </data>
  <data name="toolStripMenuItem小節の削除.Text" xml:space="preserve">
    <value>小節の削除(&amp;D)</value>
  </data>
  <data name="contextMenuStrip譜面右メニュー.Size" type="System.Drawing.Size, System.Drawing">
    <value>215, 198</value>
  </data>
  <data name="contextMenuStrip譜面右メニュー.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAkAMDAQAAEABABoBgAAlgAAACAgEAABAAQA6AIAAP4GAAAQEBAAAQAEACgBAADmCQAAMDAAAAEA
        CACoDgAADgsAACAgAAABAAgAqAgAALYZAAAQEAAAAQAIAGgFAABeIgAAMDAAAAEAIACoJQAAxicAACAg
        AAABACAAqBAAAG5NAAAQEAAAAQAgAGgEAAAWXgAAKAAAADAAAABgAAAAAQAEAAAAAAAABgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAD///8A+vr6APjDxQDyjo8A4Q8PAHIICADvUFEAfmlpAPHn5wDKxsYAnZqaACrp
        7wB58PQAyvn7AP///wAREREREREREREREiEhERERERERERERERERERERERIiIiERIi4iEiERERERERER
        EREREREhESIiIiLu7d3u4iIREREiIiIRERERISIiLu7d3e3dzMzM3e7u7u4iIiIhIhESLu7t7dzMzMzM
        zMzMzN3d3d3d3e7iIhEiLu3dzMzMzMzMzMzMzMzMzMzM3N3d4iERLu3czMzMzMzM3buM3d3d3d3d3d3e
        4iEREi7t3MzMzMzd6riKru7u7u7u7u7uIhERERIu7u7u7u7umoaKqRERIREhIRERERERERERESEpkpKS
        pIZoqZIRERERERERERERERERERk5ozmaSGZmizIREREREREREREREREREZk3RLOkZmZmaLqRERERERER
        EREREREREpl1Z3RGVmZmYIqZERERERERERERERERIpN1VYd2ZmZmZmipIhERERERERERERERKTRFVVdl
        ZWZmZmaqmqqpmSERERERERERmTRHVVZVZmZmZma0uIu6qZkhEREREREikzR0VVVWVWZmZmiIZgYIi6qS
        IRERIpOTNHdVVVVlZlVmZmZgYGBgAIuikRESKTNEdVVVVVVVVWZWZmZmZgYAYAi6mRESk3d1dVVVVVVV
        ZWVmVmZmYGYGAGBoopERmTR3VVVVVVVVVlZWZmZmZmBgYAAAipESKZNFdVVVVVVVVVVlZWZmZgYGBgBg
        C6ERIiNHdXVVVVVVVWVWVmZmZmZmBgYACKERESk3dVVVVVVVVXVlZWVmZmZgYAAACKIREik3d3VVVVVV
        d3R3ZWZmZmZmZmZoupERESmUd3V1VVVVczREREt4eIiIi7uqqZERESKUd3dVVVVVczM0QzMzo6OqOpmZ
        mSERERKTR3d1VVVXQ5mTOZKSmSKSkpGRkREREREpNEd3VVVXQ5kpkpGRkpkZkREREREREREpk0d3dVVX
        Q5kpERESIRIREiIiEREREREpNHR3dXVUQ5kiIiIiIiIiIikiIhERERIpNEd3d1d0qpkimZkimZkpKZmZ
        kiERERKZRER3d1VLq6qZmamqmqqqqqqqmSIRESKTRERHd3eLi7qqqqqqu7u6uru7qSIRERKTNERHR3iI
        iIuqq7qruIi7u4i7upIRERKTREREd3oimou7mrupqLqripmrqpIRESKTM0RER0kiIqu7IruiKCK4kiIq
        qpIRESKTMzREREkqgiiLIruyKS6LIooiqpIRERKTMzQ0RKkriiq7Iou6IiiKKouqqpIRERIjMzNEQ6kr
        ixqIIou7IpiKKoi7upIRERGZMzM0Oakqiiq7IoiLIii5KriqqpIRERIpkzMzmZkqsiu7Iou5Iiq6Kbsi
        qZIREREpk5MzkpIiIpoiIimiKhKqIpIpqSEREREimTOZIiIiKZkiIiKSmZKZkiKZkiEREREimTmSIRIi
        IiIikpKSIiIiIiIiIRERERESKZkiIRERERERERERERERERERERERERERIiIhERERERERERERERERERER
        EREREREREREREREREREREREREREREREREREAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAIAAAAEAA
        AAABAAQAAAAAAIACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wD6+voA9Ly+APKMjgDhEBEAdQ0NAO1R
        UgDu5OQAf3p6AKmmpgDR0NAAOenvAIjz9wDP+vsA////ABERERERESIiEhERERERERERERERIiIu7eIR
        EREREREREiEi7d3d3MzN3u7u7iIhERLt3czMzMzMzM3c3d3d7iEe7czMzMzMyZzMzMzNzd4hES7c3MzM
        25ab7u7u7u7iERERIu67uIRmaYIREREREREREREjekuWZmabIREREREREREShFV0VmZmaoIiESERERER
        IoRVVlZmZma+u7giERERERg0dVVWVmZplmaauBEREog0dVVVZWZmZmYGAGq4IihHd1VVVVVWZmZgYGAA
        myEoN1VVVVVWVWVmZmYGAAmyIiN1VVVVVVZWZmZgYAYAkhEjd1VVVVVVZmZmZmBgAKIRI0d1VVVXRHd3
        ZmZmmZqyESJHd1VVVIhDizszu7iIIhEShEdXVXSIiIIogigiIiERESNHdVV0u4iIiIi4uIgiERKER3dV
        dLu4i7u7u7u7ghEihER3d6qqqqq6qqqqq7gREjREd3eqmZqqqZmZmaq4ERgzREd6i6mbqbubqbuquxEi
        M0RESygqkrmyspIhGrgRIjMzRDgpK5G5kiiRqbu4ERKDM0OIKiiRuZgroZqquBESiDM4iCsrooqiKKK7
        K4gREYgziCIiKCIigYGyIiiCERIoiCESIigoKIiIKCKCIhERIoIhEREREiIiIiIiERERERERERERERER
        ERERERERAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAoAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAA////AAD/
        /wD/AP8AAAD/AP//AAAA/wAA/wAAAAAAAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAGcAAAAAAAYGd2AAAAAABnZ2cHAAAABmZmdnZ3AABmZmZ2d3dwAGZmZmZ2
        d3AABmZgAAAAAAAAZmAAAAAAAAZgAAAAAAAABgYAAAAAAAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAACgAAAAwAAAAYAAAAAEACAAAAAAAgAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////APr6
        +gBoZ2gA//z9AP319wD/+/wA/vr7APLu7wD76u0ATEZHAPrn6gD99vcA8uvsAPvh5AD7294A++XnAP/1
        9gD+9PUA/fP0APvHygD5ztEA+9fZAP/v8AD87u8A+Z6hAPuusQD7ur0A+b/CAPvCxQD019gA+uHiAP7m
        5wD96OkA/uvsAPlUVgD6b3EA+nd5AO9zdQD6fH4A121uAPqChQD6hogA+ouOAPqOkQD7kpQA+paZAPqb
        nQD6pacA+bO1APq2uAD7zM0A+9LTAPkGBgD2BgYA8QYGAOsGBgDkBgYA+gcHANoGBgDSBgYAywYGAMQG
        BgC8BgYAtAYGAKwGBgCkBgYAnAYGAPoKCgCTBgYAjAYGAIQGBgD6DA0AewYGAHIGBgBqBgYA4Q4OAGIG
        BgD6EBAA2w4OANcODgBaBgYAyw8PAL8ODgBSBgYA+hUVAEcGBgCbDg4APAYGAPkaGgDpGRkAgg4OAHoO
        DgA0BgYA2BoaAPohIgDHGhoAaw4OAO4gIAAsBgYAuhsbAKgZGQD6KCgAkhcXAPInJwBWDg4A3iYnACMG
        BgD6LCwA+i8wAPAuLwD6MzQA+js7AOY4OQDOMzMAeB4eAPpAQQD1Pz8AZRsbABkHBwD6SEkATxcXAL44
        OAD4T1AAzkVGAOZPTwD4Xl8A3VRUAEYbGwD6Z2gA62hpAIs/PwCkT08AcTs7APKEhAA2Hh4A84iIAIhP
        TwC6bW0ATi8vAN6IiADxlJQA0YiIAPGengCUaGgA3J6eAPO1tQDzwMAAq4iIAJZ6egDbtrYA8czMALmd
        nQD209MA68zMAP7f3wBeU1MA/eLiAP7k5ADItrYA3cvLAPTi4gB0bGwA/u7uAPjo6AD66+sA49XVALes
        rAD+8fEA3dPTAPvx8QD57+8Avra2AP/39wD27u4A//j4AOfg4ADo4uIA/vn5APDt7QDPzMwAoqCgAI2L
        iwDV09MA//7+AK+urgAdExIAKiAfAPP+/gD6//8A/f//AP7//wAK5+8ADejwABHo8AAU2+EAGenwACXq
        8QAj3OIAJcLIADHr8gA67PIARe3zAFbv9ABm8fYAbvH2AGfi5gB58vYAiPT3AGasrgCX9fgAq/f6ALPq
        7ADD+fsAyfr7AM77/ADH7O0A1fv8AN38/QDT7O0A4/z9AHTi5wBpxckAvPj7AOv9/gC30dMA+P7/AP39
        /QD7+/sA+Pj4APf39wD09PQA8fHxAPDw8ADv7+8A7OzsAOrq6gDn5+cA5OTkAOHh4QDb29sA1dXVANLS
        0gDQ0NAAysrKAMXFxQDAwMAAurq6ALW1tQCpqakAm5ubAJKSkgCGhoYAgICAAHl5eQD///8AAQEBAQEB
        AQEBAQEBAQEBAQEBAQG/vr6+vr8BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAb++vr6+vr6+
        vr284Nra4Ly9vr8BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAb+/v76+vbzg3Nrc3ODg2tff0tDS09XZ3ODi
        vb29vb29vb6+vr++AQEBAQEBAb++4uK8vLzg2tfT0tLS0tPS0M3LycjIyszP0t/X2dnZ2dnZ2trc4ODi
        vb6+vwEBvr3g2tnW39PT0s/LysjIycrJyMXEwcDBw8TFycvNz8/Pzc/P0NDS0tPf19rg4r4Bvbzc1tPQ
        zMvKyMXEwsDAwMHBwcTFxcbHx8bGxcjJycnJycnJysrKy8vN0NPX4L2/vr283NXSzMnFxMLBwMDAwMDC
        xcrO3d7RCt7Ozc3NzczMzM3Nzc3Nz8/Q0tPW4L2/Ab++4uDZ09DMy8rKysrKysrLzNLU4bWFouHh2NfX
        19fX19fX19fX2dnZ2drg4r4BAQG/v77i4NrX1dXf39/f39/V1tvvn5RUiZ+36r29vb3ivb29vb29vb29
        vb6+vwEBAQEBAQG/vr694uLi4g0ICAji6+ugmINNdpCfsQgBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEYmZeXl5qhoZqRhEpLTXaJrKkIAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBARgfkiiMkYya
        lo5nR0pKTVFRiZipCAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAR+Xgj1gfCiRjmVFR0dKSk1RVIWf
        7wgBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBF50zgjw9U3x8ekNDRkdJSktNUWmQoLG/CL8BAQEBAQEB
        AQEBAQEBAQEBAQEBAQGjozSPiF48PXJ8ZEFDQ0ZHSUpLTVGFn6mpt6C0t+7p6QEBAQEBAQEBAQEBAQEB
        AQEXnTOIiH1QPT1gZEFCQ0VGR0lKS012lJ+YkKK2mKy0t+/qAQEBAQEBAQEBAQEBARedMzIqgoJxPD1S
        U0BBQkNFRklJSmGJkIV5WF1jh5yitfjy6QEBAQEBAQEBqCAWFjMyL4F1Wm5aTzxSPj9AQUJDRkZJXHNz
        UVRWWFhdY2trh6K19OrqAQEBARESmxQaLi0kXzU2Nzg5TDs8PT4/QEFCQ0VHW1xLTVFUVlZYXWNrd7oK
        trTv6gEBAREiM4F7cHBmTkQ1Njc4OTs7PD4+P0BCQkVGR0lKS01RVFZYWF1ja3d3uwO06uoBAQGjmzMs
        dGxfWU5ENTc3ODk7Ozw9Pz9AQkNFRkdJSktNUVRWWF1dY2t3d7oDtO8BAa2to5sbJ3BsX1VOOjY3Nzg5
        Ozw8Pj8/QUJDRUZHSkpNTVRUVlhdY2trd3e6tfIBAQGtERGbLXRvZl9VSDo2Nzg4OTs8PT4/QEFCQ0VG
        R0lKS1FUVlZYXWNra3d3nPIBAQEBARGeGnt0b2ZZVUg1Njc4OTk7XnJgU0BBQkNFR0lKS01RVFZWWF1j
        a2t3orQBAQEBra0gHYF4cG1mWU5ENTY3ODlxgoIoKHJ6ZWVXRUZJSktNUVFWeYCAi5y2rO8BAQEBAa0X
        NCojeHBtX1lORDY2N1qCkpKPjIyMjI6OioSEhISEkJCQlJiYrKy07+oBAQEBAa2onjCBI3hwbF9VTjo1
        N2KCMzSZlyiPl5eampqWlpagoKCgoKmxsLEICAgBAQEBAQGvITMrfnt0dGxfVUg6NnWIMx8fH5mZoaur
        q6urrq6urq6urggBCAEIAQEBAQEBAQEBrSAUMCuBeHBmX1VINXsqMZ0YGBgfGBgBqwGrq66uAa6uAQEB
        AQEBAQEBAQEBAQEBr6gWGy1+eHRvZllOVYEqMZ2joxgBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEB
        siI0GSSBI3hwbV9ZaIaNl6Sl4+Pj5ALkAgICAgICAgICAuXm5gLjuAEBAQEBAQGyEp0aKiUkfiN4cG5i
        f5GWoKbr5+fn6urq6uvs7e3t7Ovr7O7v7+3p5uMBAQEBAQEHqDQuLCklgX57dHFqjpinp5/w7u3u8PDw
        8PDy9PT087e38/X29rTw6+bjAQEBAQatITMZLispJYF+e3FyipX9trX38/K09vb19fa5tbW1+fj4ubX6
        +rn17+nkAQEBAQESnhswGS0rJySBI3yKlaKiov21+Pf3+fn4+Ln7/f79tvr6tvz9/Pr48uwCAQEBAQQS
        IRwaMC8tiCckfn+gAgLp+P62tfnu8vr58+74A/ny+Pz89Ozv9vr48+3lAQEBAQQREB0yGhkvLCongiiw
        AgICAvH8+voCAvv58wLknAIC+/3vAgICArf38u3lAQEBAQStDhQcMRoZLiwpJpGwArn+AgL9tvsCAvy1
        +QIC7gLuorUCAv33AgL2t+3lAQEBAQSyIhUUGzEwGS4rjZbuArkD8gL4tvwCAv62+vUCAgL9ovIC9v22
        9vP38u3lAQEBAQSyqBYzHRsxMBkuk6DtArkD+QL3tvwCAqL8+/sCAu4D/vAC9/39trX48+3lAQEBAbgE
        Ew+ZMx0bGjAdHqnsAvgD8QL2+/wCAqL9/LUCAgL+tvAC9vz9+PX48uwCAQEBAbgEDBAWNBQcMjEPpLGz
        AvX6AgL5+foCAv22++8CAgL1+fMC7Pq1AgL18OnkAQEBAQEErQkPFhUUHA8gqg3nAgICAuv0AgICAgLr
        9gIC8QIC9PICAugCAu7w6+bjAQEBAbi4BxMODx4VFiCosgLmAgLl6+/uAgICAgLn7ALo7+cC6+zr5gIC
        6+7q5uMBAQEBAQG4BgUJHw8WIhMGAePk5ufn5+bl5efn6Ono6Ofn5ubm5uXl5ebn5+Xk4wEBAQEBAQEB
        uAcTCw4YDLIGAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAeMHsgyyBwQGAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAbjjBAQEAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKAAAACAAAABAAAAAAQAIAAAA
        AACABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A+vr6AP/6+wD++foA++nrAP309QD07O0A+trdAPmw
        tAD5w8cA+8fKAPrO0QD8290A+9/hAP3t7gD5naAA+qOmAPm1uAD6vsEA+dLUAP3k5QDr2NkAXRITAPpO
        UAD1WVsA+l9hAPl0dgD6f4IA1G5vAPSFhgD7i40A+I+QAPuTlQDhhIUA+ZWYAPqanAD7qKoA/Le5APzM
        zQDzBgYA7AYGAOMGBgD5BwcA2wYGANMGBgDOBgYAxgYGAOYICAC7BgYAsgYGAKsGBgCkBgYAnQYGAJMG
        BgCMBgYAywkJAIQGBgD6DAwA6QsLALYJCQB5BgYAdQYGAG4GBgDwDg4ArAoKAGkGBgDRDQ0AYQYGAF0G
        BgB/CQkAUwYGAPoTEwDHDw8AuQ8PAEwGBgDaExMARAYGAFkICADDExMAOwYGAPobGwDhGBgA8xsbADIG
        BgCOEhIA+iIjAC0GBgDpIyQA+igpAPImJgAmBgYAVg4OAKgcHADxKSoA+i0tAMonJwD6NDUA5TExAPI1
        NQD6PT4AchwcABoHBwCNJycA+UhJAOJERAD6VVYA+lpbAMNNTQB0Li4A+WprAN5eXgDsZmYAJBQUAHxJ
        SQDxl5cATTExAPCdnQDilZUAxoyMAOmsrACZdHQAnn19AMuhoQB5YGAA9MPDANy0tACrkJAA/tfXAOfF
        xQD+3t4A8dLSAPrc3AD03NwA3MnJAP3o6AD25OQAzMHBAKyjowD98fEA39XVAJyVlQD/9fUAkIqKAP/2
        9gDm3t4A//j4AIB8fAD48vIA6uXlAMzIyAD08PAA//39AP78/AD59/cA9fPzAO3r6wDn5eUA//7+AP38
        /AD29fUA7+7uAOfm5gDh4OAA2djYAM7NzQCPjo4AIwgHAGtqaQDt/v4A9f7+APT9/QD2/v4A+f//APv/
        /wD8//8ABOfvAA7c4wAQ5+8AFr/FAB3p8QAc4+sAFrK3ACbr8QA36/EAQO7zAE7u9ABR7/UAUO3yAE3h
        5gBU7/UATd3iAFfw9QBa7/QAZvH1AHLx9gBQpqkAgvP3AIft7wCW9fgAp/f5AML5+wDO/P0Az/r7ANX8
        /QDO9PUAt9bXANr8/QBb7fMAXe/1ALf4+wDJ+fsAzfr8ANL7/QDl/P0A2+ztAPL+/wD3/v8A1tncANna
        3ABUVFUA+/v7APn5+QD4+PgA9vb2APX19QDy8vIA8PDwAOzs7ADo6OgA5OTkAOPj4wDe3t4A29vbANbW
        1gDU1NQA0dHRAMrKygDIyMgAx8fHAMHBwQC+vr4Au7u7ALi4uACxsbEAra2tAKqqqgClpaUAoaGhAJyc
        nACUlJQAioqKAISEhAB6enoAc3NzAP///wABAQEBAQEBAQEBAQEBAa6uAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAa+s2KutqqnP0tLMqa0BAQEBAQEBAQEBAQEBAa6srKqpz9LHx8jIxdG4uNDFyNTPzMzMz9apqq8B
        AQGt1tPIx8XCube0t7e0srG2s7W4vtHBwdHCw8XH0s/ZAa3WycW5tLKwsLCwsLe9xNzEvby+u7q8vsDR
        wsPHyakBAa/Y1MfCwLu6vLzBxs56F4/OzcvK1MrLy8vVzM/WrQEBAQGv2NbW19rq2tfpe2dEZX+R3wEB
        AQEBAQEBAQEBAQEBAQEBAYt9byJ2gXdVPT9EF3mM3gEBAQEBAQEBAQEBAQEBAQGZh3VDSWwdXTY5PUJF
        XH+Vn58BAQEBAQEBAQEBAQEBmIsMc2IuYGBBNTY5PkJFcoaMiaXooeEBAQEBAQEBAQGSFRMccEwuTzwz
        NTc5PkJyenJ0dHyN8KOfAQEBnpAVgAsQa1pYUi04MTI0NTdGZWVOS01UV6d0puninwGQgh9ua1krKCkw
        LC0vMTI0NjdGP0RHS1BUW2ZxqOygnwMVJhpfUUgrKCkqLC0vMTM0Njk9P0RHTVBUW2Zm3OvgA44PJWRZ
        UTorKCkqLC4vMTM1Njk9QkVHTVBXW2Zmpt8BnpILamRZUTorKCkqTGBgSjM1Nzk9QkVHTVRXW3GP3wGY
        kA0baGFWSDorKDtwcyIdbGxsZ2dnZW1tcnyTiqQCAQGSDxFraGFWSCsoUyCEFHZ4g4OBfn6GhoaMlZWc
        l90BAQGQgCFuGGFWSCtjc4OIiIUHlJSXBweXnJvg4JqfngEBAZiLJyBqZF9TQGkigZGRnJycnKKdoubn
        5uei5OICAQEBAxUlGxoYZF5YHXuJluyk6urqpaXt7+7v76XqouIBAZ6QJyEcGxpoYml7ior39PLz8/L1
        9vf5+Pj39PCk5AEBmA8TECAcbhlpHX+m+/ym+fn5+Pr7k/2Tk/v59aXmAQGZDxMlJCAccG9+6KT0qP7t
        8/ul8P7r9pPtpPX376MBAZgPChIRJB8bIowC5QL4qALq/aQC7QL8AgICAvTuowEBmIsMEwkRIx5+kQL7
        Aur+Aur9kwICov0C9vro8e3nAQGYkAgnEwkREhaiAvcC6PsC6fz86QLy8gKm+vfz7OUBAZ4EFRQLExIO
        B6EC6wLo9ALn9/QCAqH1AqTvAqSj4wEBAQMPCBQLDYua4AICAugCAgIC5ALkAuniAgLh6OTgAQEBmAYV
        CA6LmZ7d3+Hi4+LioeTkoeXioeTi4qHi358BAQEBmYsFBgOYAQEBAQGen90C3t4CAt2fn5+fn56eAQEB
        AQGemZmYAZ4BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAEAAAACAAAAABAAgAAAAAAEAB
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wD99PYAzMfIAPro6wD+9/gA+tfaAPzn6QD/9fYA+cbJAP7s
        7QD6hYkA+ZyfAPqsrwD5srUA+sjKAPvR0wD71NYA/uLjAPpJSwD6TE4A+l9hAPplZwD6enwA+oKEAPuF
        hgD5lpgA/bGyAPyyswD9yMkA/M3OAPkGBgD2BgYA9QYGAO8GBgDtBgYA6gYGANwGBgDPBgYAxwYGALUG
        BgCyBgYAoQYGAJ8GBgCaBgYAywgIAK0HBwCVBgYAjQYGAPoMCwCJBgYAhQYGAIEGBgB7BgYAdgYGAHAG
        BgBoBgYA+g8QANkNDQBjBgYAYgYGAF4GBgBZBgYAVAYGAOAREQBQBgYA+hQUAEsGBgD6GBgAPgYGADoG
        BgA2BgYAKgYGAPomJwDKHx8AJwYGAPorLAB1FRUAwSMjAPowMQD6PD0A+0JCAPpCQwD8UVEAzkJCABUI
        CADlbm4A84iIAPmSkgDxjo4A4YmJAP27uwDfpaUApoKCANuvrwDMo6MApIWFAPbKygDpwcEAsJKSANm4
        uADTu7sA/uPjAJ6NjQD+5eUA/+rqAP7r6wDVxsYA+OnpANjMzAD98PAA/fLyAP709ACJhIQA8+vrAODa
        2gD/+voA/fj4APv29gDs5+cA//v7AP76+gD8+PgA/Pn5AP/9/QD49vYA7+3tAP/+/gD+/f0AIwgHADAi
        IQD5+fkA6enpAM/PzwDOzs4A////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQFyXXcBAQEBAQEBAQEBdWx2
        ZDxjfQEBAQEBAQEBeVZUXDA2PWB9AQEBAQEBAWhZJk4qMjc+bWcDhAEBZltTIkAtKCszTT9FSIKGgxJQ
        RB8jJScpLDQ4QUZLVYV4GU9CICQ6Si4vNTtDR4FxdB0UTDkhV2FaYl5fZWtzfgEIHBNJMVhub4ABensB
        AQEBCgsWUlFpAQEBAQEBAQEBARAMGBUbAQEBAQEBAQEBAX8RDhoXagEBAQEBAQEBAQF/BwkNHnQBAQEB
        AQEBAQEBAQIGD3ABAQEBAQEBAQEBAQF8BAV/AQEBAQEBAQEBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKAAAADAAAABgAAAAAQAgAAAA
        AACAJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAIAAAADAAAAAwAA
        AAIAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAABAAAAAgAAAAMAAAADAAAAAwAAAAIAAAACAAAAAgAAAAMAAAAGAOvrDQDf
        6hgA6PAhAO73HgDk8hMA5uYKAAAABQAAAAIAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABAAAAAQAAAAEAAAABAAAAAgAAAAQA5uYKAObyFADl7R0A5u8fAO3tHADg6xkA5/MVAOn0FwDo
        8CEA6e8vBOXwRQPl7WEC5+52AujvcAPn8FUA5u48AObzKQDs9hsA3+8QAAAABwAAAAQAAAAEAAAABAAA
        AAQAAAAEAAAABAAAAAMAAAADAAAAAwAAAAIAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABAAAAAwAAAAcAAAAJAObmCgDo6AsA7e0OAObyFADm7x8A5vAyAOjyTgLp8GgC6PFwAujvbQPo
        8GQD6e5bA+fvYQLl7nYC6PGOAufwqAPm7sMC5+/TAubuzwHm7rkC6O+eAubwhALn7mkD5O5LAObwMgDm
        7CkA5vMpAOftKwDn8ysA5/MqAObsKADj6iUA6PAhAOXtHQDp9BcA4fARAOjoCwAAAAYAAAADAAAAAgAA
        AAEAAAAAAAAAAAAAAAIAAAAGAN/vEADe7x8A6O4sAOjxNwTn70ED6O5MA+jxWgLo724C5u6HAubupQPn
        778D5/DMAubvzgPn8McB5+/AAejuxQLn7tMC5+/fAufv6QLn7vIC5u73Aubu9gXZ3/AC6O/lAubv2QPn
        7sYC5u+qAubvjwLm7oUC6PCGAufuigLn7osC5+6JAubuhALn730C5+52AuXvbQPn72EA5u9SAOfvQQDq
        7zAA5+8gAOPjEgAAAAcAAAACAAAAAAAAAAQA6OgLAOPsGwDm6zQA5u1TAufwdwLo8JgB5u+wAefwwALn
        784C5+/bAufu5gLo7+8C5+72Aufv+ALm7vcC5+/0Aufu9QLn7vMC5/DqAufv4QLn790H19/gD7vB5g29
        wukG19/lBdff4gLo79oC5+7RAefuyAHo78UB5+7HAefvyQHn78kB5+7HAebuxAHn78AB5++8AefwtgHo
        764C5u6jAubvkQLo7ngD6PBXAOrvMQDm8hQAAAAFAAAAAQAAAAIAAAAGAO3tDgDm7h4A5e06AubwZwLo
        8J0D5+7HAufv3QLn7+oC5+/xAufv9ALm7vYC5u73Aubv+ALm7/gC5+/2Aufu8ALn79sD6O+6DNHYoA3N
        1JIcn6OhIYeKsDYzNeARq7KpB87VmwLn7pIC6O+RAufvkwLp8JUC5/CXAubumQLo7pgC5/CVAufvkwLo
        75IC5u+RAufvjQLn8IkC5u6FAujxfALm728D5/BWAObrNADo6BYAAAAFAAAAAQAAAAAAAAABAAAAAwAA
        AAcA4/ESAOfzKgPp71EC6PB6AujwmALn8KoB5+61AejwuQHm77sB5++8AejvvAHo77wB5u+7AebvsALn
        7pQC5+5sFb3EUzRxdlZHNzh+TRITz0EgIbAlaG1dGIiOSA6srzsA6e4uAOTvMADq7zEA6/AyAOvwMwDm
        8DMA6u8xAOrvMADk7zAA6e8vAOPuLgDo8ywA5+0rAOftKgDl6ycA5+cgAObmFAAAAAgAAAACAAAAAAAA
        AAAAAAAAAAAAAQAAAAEAAAADAAAACADm8hQA4/EkAOXvMQTl7ToE5+8/BOfvQQTo8EIE5OxDBOTwQwTk
        8EME6PBCBOfvQADo7TgpoKU0SF9fMGIZGVJgCwx9WQYG/1AJCbZEExZQOxoaMis1NRUAAAAFAAAABQAA
        AAUAAAAFAAAABgAAAAUAAAAFAAAABQAAAAUAAAAFAAAABQAAAAQAAAAEAAAABAAAAAQAAAADAAAAAgAA
        AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAIAAAADAAAABQAAAAYAAAAHAAAABwAA
        AAeDRE4YfUROGHdEThhwRE4YAAAACFpXVxleREQYdg8PNnEJCWRpBgbFYAYG/1gGBudPBgabRwYGTD8G
        Bh44BgYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAzgYGEMYGBi2/BgY1tgYGNK4GBjSmBgY0nQYGHo8ODh+LBgY0ggYGY3kGBrRwBgb/aAYG/18G
        Bv9WBgbvTQYGtEUGBks+BgYtNgYGEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADbBgYQ1AYGHswGBkzFBgabvgYGerUGBmOtBgZ6pAYGNJsGBkuTBgZ6igYG7oEG
        Bv94Bgb/bwYG/2UGBv9dBgb/VQYG/0wGBrNEBgZjPAYGLTQGBhEAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgBgYf2gYGNNMGBpvLBgb/xAYG77wGBrSzBgaarAYGY6IG
        BnqaBgbmkAYG/4gGBv9/Bgb/dgYG/20GBv9kBgb/XAYG/1MGBv9KBgbFQwYGTDoGBh80BgYQAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOsGBhDlBgYe3wYGNNkGBpvRBgb/ygYG/8IG
        Bu+6BgbEsgYGtKkGBsWhBgb/mAYG/48GBv+GBgb/fQYG/3UGBv9rBgb/YgYG/1oGBv9RBgb3SQYGm0EG
        BjU6BgYeAAAAACsGBhAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA9AYGEe8GBhHqBgYt5AYGY94G
        BnrYBgbv0AYG/8gGBv/BBgbVuAYGxLAGBuaoBgb/nwYG/5cGBv+OBgb/hAYG/3sGBv9zBgb/aQYG/2EG
        Bv9YBgb/UAYG1UgGBktABgYtOAYGLTEGBi0pBgY1IwcHNB0HBy0XBwceEQgHEA0ICBAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+AYGEPMG
        Bh7uBgY06QYGeuMGBnrcBga01gYG988GBv/HBgb/vwYG57cGBu6vBgb/pgYG/54GBv+VBgb/iwYG/4IG
        Bv96Bgb/cQYG/2gGBv9gBgb/VwYG5k4GBnpHBgZLPgYGYzYGBpsvBgaaKAYGeiIGBmMcBgdLFgcHNBEH
        CC0NCAceCAgIEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAD6DQ4Q+gcHHvcGBjXyBgZL7QYGeugGBpriBgab3AYG1NQGBv/OBgb/xgYG9r0GBve2Bgb/rQYG/6UG
        Bv+cBgb/lAYG/4oGBv+BBgb/eAYG/3AGBv9mBgb3XgYGs1UGBptMBgbFRAYG7jwGBv81Bgb/LQYG/ycG
        BucgBwe0GgcHmhUHB2MQBwhLCwgILQgICBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPpB
        QhH6NDUe+ikpLfodHi36FBQ0+gwMS/oGBmP3Bgab8gYGxewGBu/nBgbV4QYG5toGBvfTBgb/zAYG98QG
        Bv+9Bgb/tAYG/6wGBv+jBgb/mwYG/5EGBv+IBgb/fwYG/3cGBvduBgbmZQYG5l0GBv9UBgb/SwYG/0MG
        Bv87Bgb/NAYG/ywGBv8mBgb/IAcG/xoHB+cUCAeaDwgIYwsICDUJCAgRDQgIEQAAAAAAAAAAAAAAAAAA
        AAD6ZWcQ+llaEfpLTS36P0BL+jIzY/omJ3r6HB16+hMTm/oLC+b5Bgb/9QYG//EGBv/sBgb/5gYG/+AG
        BvfZBgb/0gYG/8oGBv/CBgb/uwYG/7MGBv+qBgb/ogYG/5kGBv+QBgb/hwYG/34GBvd1Bgb3bAYG/2MG
        Bv9aBgb/UgYG/0oGBv9CBgb/OgYG/zMGBv8rBgb/JAYG/x4HB/8YBwf2EwgIxQ4ICHoKCAg0CggIHg4I
        CBEAAAAAAAAAAAAAAAD6b3IR+mNkH/pWV0v6SUrV+js95vowMO/6JSbn+hoa7voREv/6Cgr/+QYG//UG
        Bv/wBgb/6gYG/+QGBv/eBgb/2AYG/9EGBv/JBgb/wgYG/7kGBv+yBgb/qQYG/6AGBv+XBgb/jgYG/4YG
        Bv98Bgb/dAYG/2sGBv9hBgb/WQYG/1AGBv9IBgb/QAYG/zkGBv8xBgb/KgYG/yMGBv8eBgf/FwcH/xMI
        B+YNCAiaCQgINAsICBEPBwcRAAAAAAAAAAAAAAAA+m1vHvpgYjT6U1VM+kZIm/o5O/f6Li7/+iMj//oY
        GP/6EA//+gkJ//gGBv/zBgb/7gYG/+kGBv/kBgb/3QYG/9cGBv/QBgb/yAYG/78GBv+4Bgb/sAYG/6cG
        Bv+eBgb/lgYG/4wGBv+EBgb/ewYG/3EGBv9pBgb/YAYG/1cGBv9PBgb/RwYG/z8GBv83Bgb/MAYG/ykG
        Bv8jBgb/HAcH/xYHCP8RCAf2DAcImgkICDQLCAgeAAAAAAAAAAD6g4YR+nd6EPpqbR76XWA0+lFTY/pE
        RbP6ODj/+iwt//ogIf/6Fxf/+g8P//oIB//3Bgb/8wYG/+4GBv/oBgb/4gYG/9wGBv/VBgb/zgYG/8YG
        Bv++Bgb/twYG/64GBv+lBgb/nQYG/5QGBv+LBgb/ggYG/3kGBv9wBgb/ZwYG/18GBv9WBgb/TgYG/0UG
        Bv89Bgb/NQYG/y8GBv8nBgb/IQYH/xsHB/8VBwf/EAgH7wwICGMICAgtAAAAAAAAAAAAAAAA+oGDEPp1
        dxD6aGoQ+lxeNPpOUJv6QkP/+jU3//opKv/6Hx//+hUV//oNDf/6Bgf/9wYG//IGBv/tBgb/6AYG/+EG
        Bv/aBgb/1AYG/80GBv/FBgb/vQYG/7UGBv+sBgb/pAYG/5sGBv+SBgb/iQYG/4AGBv94Bgb/bwYG/2YG
        Bv9dBgb/VQYG/0wGBv9EBgb/PAYG/zQGBv8tBgb/JgYG/yAGB/8aBwf/FQgH/w8ICLMLCAgtAAAAAAAA
        AAAAAAAAAAAAAAAAAAD6c3UQ+mZoLfpZW3r6TU32+j9A//ozNP/6Jyj/+h0d//oUFP/6Cwz/+QYG//UG
        Bv/xBgb/7AYG/+cGBv/gBgb/2gYG/9IGBufLBgbVxAYG5rwGBvezBgb/qwYG/6IGBv+aBgb/kQYG/4cG
        Bv9/Bgb/dQYG/2wGBv9kBgb/WwYG/1MGBv9KBgb/QgYG/zsGBv8zBgb/LAYG/yUHBv8fBwf/GQcH/xQI
        CJoPCAg0AAAAAAAAAAAAAAAAAAAAAPmIixH6fX8Q+nFyLfpkZWP6V1nu+kpL//o9P//6MTL/+iYm//ob
        G//6ERL/+goK//kGBv/1Bgb/8AYG/+sGBv/lBgb/3wYGxdgGBpvRBgaayQYGmsIGBpq6BgbFsgYG1KkG
        Bu6gBgbumAYG948GBv+GBgb/fQYG/3QGBv9sBgb/YwYG/1oGBv9SBgb/SQYG/0EGBu85BgbmMgYG7isG
        BtUjBga0HgYGehgHB0sSCAceAAAAAAAAAAAAAAAAAAAAAAAAAAD6hokR+np9HvpucEv6YmTE+lVW//pH
        Sf/6Ozz/+i8w//ojJP/6GRr/+hAR//oJCf/4Bgb/9AYG//AGBv/qBgbu5AYGm94GBkvYBgZM0AYGY8kG
        BnrBBgZ6uAYGerEGBnqnBgZ7nwYGepcGBpqNBgazhAYGtHsGBrNyBga0aQYGtGEGBptYBgabUAYGmkcG
        BnpABgZjOAYGYzAGBksqBgZMIwYGNBwHBx8XCAgRAAAAAAAAAAAAAAAAAAAAAAAAAAD5jpIR+oOGHvp4
        ejT6bG6b+l9h9/pSVP/6RUf/+jg6//osLf/6ISL/+hgY//oOD//6Bwj/+AYG//MGBv/uBgbm6QYGmuMG
        BjTcBgYt1QYGLc4GBjTHBgaavwYGY7cGBjSvBgY0pgYGNJ4GBjSUBgY1iwYGS4MGBkx5BgZLcQYGNGgG
        BjVfBgY0VwYGNE4GBjVGBgYtPgYGHjYGBh8vBgYeKAYGESIGBhAbBwcQAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA+YyQEPqChC36dnhj+mlsxPpcXv/6UFH/+kNE//o2N/b6Kyv/+iAg//oWFv/6DQ3/+gcH//YG
        Bv/yBgbF7QYGeugGBjThBgYf2wYGH9UGBh7NBgYtxgYGLb4GBh62BgYQrQYGEaUGBhGcBgYQkwYGEIoG
        BhGBBgYQeAYGEG8GBhFmBgYQXgYGEFUGBhFNBgYRAAAAAD0GBhAAAAAALQYGEAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAPmLjhH6gII0+nR2Y/pnaZr6W1yz+k1P1fpAQvb6NDX3+ikp//oe
        H//6FBT/+gwM//oGBv/1Bgaz8gYGe+wGBkzmBgYe4AYGENoGBhDTBgYQywYGHsQGBhC8BgYRAAAAAKsG
        BhEAAAAAmwYGEJEGBhCIBgYRgAYGEQAAAABuBgYRZQYGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPmTlRD5iIwe+n6AS/pxc3r6ZWa0+lhZ9/pK
        TP/6Pj//+jIz//onJ//6HBz/+hMT//oLC/f5Bgaa9QYGevAGBkzrBgYe5gYGEeAGBhHZBgYRAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPmZnBD5kZQt+oaJY/p7
        fbP6b3H/+mJk//pWV//6SEr/+jw9//owMf/4JCX/9hob//EQEOfkCgqA3QYGascFBTWiBAQXuAUFFAAA
        AAIAAAACAAAAAwAAAAQAAAAFAAAABAAAAAUAAAAFAAAABQAAAAYAAAAGAAAABgAAAAYAAAAFAAAABQAA
        AAUAAAAHAAAACAAAAAgAAAAGAAAAAwAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+quvEfmi
        pB75mZxL+Y+StPqEiP/6eXz/+mxv//pgYv/6U1X/+kZH//k6O//0LS7/7CEh/9gVFbqlCgtkfgQFTEYC
        AjFYAgIqAAAAEQAAAAsAAAAKAAAADAAAABAAAAARAAAAEAAAABEAAAASAAAAFQAAABcAAAAXAAAAFwAA
        ABUAAAATAAAAEwAAABYAAAAbAAAAHwAAAB4AAAAYAAAADwAAAAgAAAACAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA+rS3EPmqrS35n6J7+ZaZ//qNkP/6goX/+nd5//pqbf/6XmD/+lFS//dDRP/tNDb/3iYn/6gW
        FpRbCAhpIwICViYBAVIvAQFGAAAAJgAAABoAAAAYAAAAGwAAACMAAAAkAAAAIgAAACQAAAAmAAAALgAA
        ADUAAAA1AAAANAAAADAAAAArAAAAKwAAADAAAAA5AAAAPwAAAD4AAAAzAAAAIwAAABMAAAAIAAAAAgAA
        AAAAAAAAAAAAAAAAAAD5xcgQ+ry/HvmxtUz5qaya+Z2h//mVl//5jI7/+oGD//p0d//6aGr/+ltd//RM
        Tv/jPD3/xyor63MTE5QxBgaFEQEBfwAAAHIAAABfAAAAQwAAADAAAAAtAAAAMQAAAD0AAAA/AAAAOQAA
        ADsAAABAAAAATwAAAF0AAABfAAAAXQAAAFMAAABKAAAASgAAAFEAAABdAAAAZAAAAGIAAABSAAAAOgAA
        ACEAAAAPAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAA+sPHLfm6vWP5sbTm+aaq//mdoP/5k5b/+YmN//p/
        gf/6cnT/+mZo//FWWP/cQ0T/pCoqs04QEJIYBASTAAAAkwAAAI8AAAB9AAAAXwAAAEgAAABEAAAARwAA
        AFUAAABWAAAATAAAAEwAAABSAAAAaQAAAIEAAACFAAAAgQAAAHMAAABmAAAAZwAAAG8AAAB4AAAAfQAA
        AHcAAABkAAAASQAAACwAAAAVAAAABgAAAAAAAAAAAAAAAAAAAAD50dUQ+srNNPnBxGP5t7rm+a6x//qk
        p//5mp7/+ZKV//mHiv/6fH//+nBz//BfYf/VSUvx2MDB0vr6+v/6+vr/8PDw+KWlpc8AAACLAAAAcQAA
        AFsAAABX3t7e08XFxcIAAABmAAAAVba2tqPe3t7TkpKSrgAAAJmRkZHNzc3N5Jubm8AAAAB5AAAAeLy8
        vMvo6Oju39/f5aWlpbUAAABjAAAASwAAAC8AAAAYAAAABwAAAAAAAAAAAAAAAAAAAAD619oQ+tDTNPrI
        y3r5v8P3+ba5//qtsP/5oqX/+Zmc//mQk//6hYn/+np9/+9pa//HTU6/5NnZ3fr6+v/6+vr/+vr6//r6
        +v/Pz8/iAAAAegAAAGcAAABk+vr6//r6+v8AAABuAAAAWba2tqP6+vr/+vr6/wAAAKT6+vr/+vr6/2Ji
        Yq4AAACB3t7e5vr6+v/6+vr/+vr6//r6+v/AwMCxAAAARAAAAC0AAAAYAAAABwAAAAAAAAAAAAAAAAAA
        AAD6298Q+tbZNPrO0pr6x8r/+b7C//m1uP/5qq7/+aGk//mXmf/5jpL/+oSG/+9zdf+2TlCO49zc2fr6
        +v+bl5fGMzMzpvr6+v/6+vr/AAAAgAAAAHAAAABv+vr6//r6+v8AAAB3AAAAXwAAAFb6+vr/+vr6/+Pj
        4/P6+vr/4+Pj8wAAAJFpaWmi+vr6//r6+v8AAAB9l5eXqfr6+v/6+vr/AAAAQAAAACsAAAAYAAAABwAA
        AAAAAAAAAAAAAAAAAAD64OMR+treLfrU2Hr6zdD/+cXI//q7v//5srb/+ams//meof/5lZj/+YyP/+16
        fdebSUpn4t7f1fr6+v+YmJjEAAAAnM3NzeT6+vr/np6evQAAAHYAAAB6+vr6//r6+v8AAACHAAAAbwAA
        AGOtra2s+vr6//r6+v/6+vr/WVlZvgAAAI/IyMjU+vr6/62trcUAAAB/AAAAdZ2dnaO0tLSlAAAARAAA
        AC0AAAAYAAAABwAAAAAAAAAAAAAAAAAAAAD64+cR+t/iLfrZ3GP609bu+szP//rEx//6ur7/+bG0//mn
        qf/5naD/+ZSX9+eBgn+KRkhR4+Hh0fr6+v+ZmZnDAAAAnJSUlMn6+vr/n5+fuwAAAHQAAAB8+vr6//r6
        +v8AAACPAAAAewAAAG8AAABs+vr6//r6+v/j4+PzAAAAmwAAAIbX19fa+vr6/6CgoLoAAACBAAAAfQAA
        AHIAAABhAAAASgAAAC8AAAAYAAAABwAAAAAAAAAAAAAAAAAAAAD65ukR+uLlHvre4WP62Nzm+tLV//nK
        zf/6wcX/+bm8//qvsv/5paj/+Zuemt6ChVFzP0A55uPkz/r6+v+dnZ2+AAAAk9DQ0OH6+vr/paWltQAA
        AGwAAAB4+vr6//r6+v8AAACRAAAAggAAAHdCQkKC+vr6//r6+v/6+vr/AAAAhgAAAHXR0dHM+vr6/6en
        p7MAAAB7AAAAf5KSkq6tra2sAAAASQAAACwAAAAVAAAABgAAAAAAAAAAAAAAAAAAAAD66OwQ+uXpHvri
        5Uv63eDE+tfa//nQ1P/6yMz/+cDD//m3uv/5rbHu+aOnY9qGiTJfNzkm6ujozPr6+v+pqamwPz8/iPr6
        +v/6+vr/AAAAWAAAAFQAAABi+vr6//r6+v8AAAB9AAAAcwAAAGrc3NzV+vr6//r6+v/6+vr/rKysrQAA
        AFmtra2U+vr6/+bm5t0AAABjQ0NDf/r6+v/6+vr/AAAAOwAAACIAAAAPAAAABAAAAAAAAAAAAAAAAAAA
        AAAAAAAA+ujrHvrk6Ev64eWz+tzf//rW2f/5z9P/+sfL//m/wv/5tbh6+aquS+GSlCGHUlQc8PDww/r6
        +v/6+vr/+vr6//r6+v/l5eXMAAAANfr6+v/6+vr/+vr6//r6+v/6+vr/6enp21lZWV/6+vr/+vr6/7S0
        tI36+vr/+vr6/wAAADUAAAAu+vr6//r6+v/x8fHl+vr6//r6+v/T09OhAAAAJAAAABQAAAAIAAAAAgAA
        AAAAAAAAAAAAAAAAAAD66+4R+untEPrn6zX65Od6+uDj9/nb3v/51Nj/+c7R//nGybT5vcBj+bO2Leqe
        ohEAAAAG8PDwn/r6+v/6+vr/9/f38eDg4JgAAAAeAAAAGvr6+v/6+vr/+vr6//r6+v/6+vr/8PDw1N3d
        3Zr6+vr/6+vrtQAAACDw8PDD+vr6/9jY2HYAAAAWoaGhNff39+D6+vr/+vr6/+Dg4JgAAAAbAAAAEQAA
        AAgAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+uvuEPrp7C355upj+ePnxfrf4v/52t3/+dPX5/nM
        0GP5xMg0+bu+EAAAAAAAAAACAAAABAAAAAgAAAALAAAADAAAAAoAAAAIAAAABwAAAAcAAAAKAAAADAAA
        AA4AAAAPAAAADgAAAA0AAAALAAAACgAAAAkAAAAJAAAACQAAAAgAAAAHAAAABwAAAAcAAAAJAAAACgAA
        AAoAAAAHAAAABAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPrq7hH56ew0+ebpe/ni
        5tT53uHm+dnce/nS1jT5y84e+cLFEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAD56u0e+ejsNfnm6Uz54uVL+d3gNPnY2x750dUQ+cnNEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAD56+4Q+ertHvno6x755ege+eHkEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP//
        /////wAA///wP///AAD/wAAH//8AAPgAAAAAPwAAwAAAAAAHAACAAAAAAAMAAMAAAAAAAwAA8AAAAAAH
        AAD8AAAP//8AAP/4QAf//wAA/+AAA///AAD/wAAB//8AAP/AAAD//wAA/4AAAL//AAD/AAAAAP8AAP8A
        AAAAPwAA/gAAAAAfAADgAAAAAAcAAIAAAAAAAwAAgAAAAAABAADAAAAAAAEAAIAAAAAAAQAAwAAAAAAB
        AADwAAAAAAEAAOAAAAAAAQAA8AAAAAABAADwAAAAAAEAAPgAAAAAVwAA/AAABQn/AAD8AAA///8AAPwA
        AP///wAA+AAAAAAHAAD4AAAAAAMAAPAAAAAAAQAA+AAAAAABAADwAAAAAAEAAPAAAAAAAQAA8AAAAAAB
        AADwAAAAAAEAAPAAAAAAAQAA8AAAAAABAADwAAAAAAEAAPgAAAAAAwAA8ACAAAAHAAD4AeOAf58AAPwB
        /////wAA/gH/////AAD+D/////8AACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAQAAAAEAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAIAN72DQDb2gsAAAAHAAAACADz
        9hEC5u0nAuLrRwHq9EgB6PApAN/2EwAAAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAJAAAACQHu7woA7PQSAOfuJgDn704C6e5pAuryaALm
        7loC5u5dAuXufALm8KQD5u7IA+PqygLk7KYC6fCAA+PuVwHj8TIA6/InAOvyKgDr9SoA6/AoAOrwIwDg
        5x0A6vQUAOzvCwAAAAMAAAABAAAAAQAAAAAAAAAGAOHuGQDl7DYB7u9UA+ftawLn74EC5+6dAufuwgPo
        79wC5u/hAufw2AHo7tsC5+/oA+Xt8Qbb4vcSsLf6Db3C9QPg6OcD5+7MAufurALm7qEC5u6mAubupwLl
        7aIC5u6ZAujxjgLl73wB5e5lAenwRADk6yIAAAAIAAAAAAAAAAYA6uwXAOTsOwLn8H0C6O+/Aufv4wLn
        7/IC5+/6Aufu/wLn7/8C5+//Aufu+gPo790M0Ni7IpabvztAQuQeg4jQBtXctwPl7K8C6fCtAufvrgLn
        77IC5+6wAufwrALo8aoC6PGkAubunQLl7o8D5u9xAeXsPwDw8hAAAAABAAAAAAAAAAMA5OwNAeX0MQPm
        7mkC6e6WAujvqQLo8bAD5u6xBOTrsgTl67MC5+6lC9nffjGIi2BWJymVVwgI9D4wMZEee4BLBsjMMgDj
        6jAA7vIxAOjtMwDs8zIA5uswAOHoMADh6S8A6/MtAOzxKwDr7yYA6+wYAAAABgAAAAAAAAAAAAAAAAAA
        AAAAAAADAPD3DQHc9hgC4OwfRpeYL2dqcD9haXNAXGhyQTeWoDBOZmczdhERZm8GBthjBgb/VgYG5EkG
        Bng/BgYhAAAABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAANwGBg3RBgY9xwYGobsGBoSvBgZvogYGO5YGBoGIBgbzewYG/20G
        Bv9gBgb/VAYG80gGBo48BgYoAAAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD4wYGF9oGBmTQBgb4xQYG9rkGBresBgaVnwYG6JIG
        Bv+FBgb/eAYG/2sGBv9eBgb/UQYG90UGBnI6BgYaAAAAAwAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvIGBg/qBgYu4gYGa9kGBtfOBgb/wwYG47YG
        BtWrBgb7ngYG/5EGBv+DBgb/dgYG/2kGBv9cBgb/UAYGwkQGBjE4BgYpLQYGPSMHBzQbBwcjEggHEQsI
        CAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH9wYGHPAGBkLpBgaB4AYGodcG
        BvHMBgb/wAYG8rUGBvyoBgb/nAYG/44GBv+BBgb/cwYG/2cGBv9aBga+TQYGiUAGBrk1BgbfKwYGzSIG
        Bp4YBwdtEQcHQAoICB8AAAACAAAAAAAAAAAAAAAAAAAAAfpHSAz6NDQe+iMjLfoUFDn6CQhl9wYGqPAG
        Bt7oBgbe3wYG7dUGBv7KBgb8vgYG/7MGBv+nBgb/mQYG/4wGBv9+Bgb+cgYG7mUGBudYBgb9SwYG/0AG
        Bv80Bgb/KgYG/yAHBv4XBwfNDwgIdgoICCUMCAgMAAAAAwAAAAD6aWoQ+lZYMfpDRJ76MDG9+iAhu/oS
        Eub6CAj/9QYG/+4GBv/mBgb93QYG/tMGBv/IBgb/vAYG/7AGBv+kBgb/lwYG/4kGBv58Bgb7bwYG/2IG
        Bv9VBgb/SQYG/z0GBv8yBgb/JwYG/x4GBv8WBwfxDwgHngkICDENCAgKAAAAAvp4egr6Zmgq+lJUavo/
        Qdf6Li7/+h0e//oQEP/5Bwf/9AYG/+wGBv/lBgb/2wYG/9IGBv/GBgb/ugYG/64GBv+hBgb/lAYG/4cG
        Bv96Bgb/bQYG/2AGBv9TBgb/RwYG/zsGBv8wBgb/JgYG/x0HB/8VBwj/DQcIsQkICCwAAAAJ+oWICvp0
        dxL6YmQf+k9RfPo8Pfz6Kyz/+hsb//oODv/5Bgb/8wYG/+wGBv/jBgb/2gYG/88GBv/EBgb/uAYG/6wG
        Bv+fBgb/kgYG/4UGBv94Bgb/awYG/14GBv9SBgb/RQYG/zoGBv8vBgb/JQYG/xwGB/8UCAf/CwgIdQAA
        AAcAAAAAAAAAA/pycw36X2BX+kxN8vo5Ov/6Jyj/+hkY//oLDP/4Bgb/8gYG/+oGBv/iBgb/2AYG880G
        BtnCBgbitgYG9qkGBv+dBgb/kAYG/4IGBv91Bgb/aAYG/1wGBv9PBgb/QwYG/zgGBv8tBgb/IwcG/hoH
        B/QSCAh5AAAABwAAAAAAAAAF+oGCEvpucT76XF7X+klK//o2N//6JSX/+hYW//oKCv/4Bgb/8QYG/+kG
        BvrgBgaZ1wYGbMsGBn/ABgaWtAYGrKcGBribBgbAjQYG24AGBuFzBgbgZgYG31oGBtNNBgbQQQYGsDYG
        BqgqBgaNIQYGXRgHBykAAAAFAAAAAAAAAAD5jJAP+n1/JvprbaH6WFr/+kVH//oyNP76IiP/+hQU//oI
        Cf/2Bgb/7wYG7OcGBnHeBgYk0wYGLckGBmy9BgZVsgYGLqUGBjGYBgY6iwYGS34GBk5xBgY+ZAYGN1cG
        BjZLBgYvPwYGHDMGBhopBgYUIAYGDgAAAAQAAAAAAAAAAAAAAAD6i44R+np8T/poarT6Vlfc+kJD7/ow
        Mfv6ICD/+RER//gHB//vBgbP3wYGasAFBSqlBQUarAUFHrMFBSOSBQUTawQEDWMEBA1bBAQQWQQEEk4E
        BBE+AwMOPgQEFCMCAgwAAAAJAAAACQAAAAgAAAACAAAAAQAAAAAAAAAAAAAAAAAAAAb5iIsg+nd5Xvpl
        Zrr6UVP6+j9A/votLv/2HR3/8A4O/+IGBrvDBAR4iwMDPVACAiROAgIfJQEBFRIBARQJAAAUCAAAFAsA
        ABkPAQEaCgAAGQQAABsKAQEdAAAAGwAAABwAAAAZAAAAEwAAAAwAAAAFAAAAAAAAAAAAAAAB+aGkDPmU
        lz36hIe1+nR2//phY//6TlD/+Dw9//EpKv/lGBn2vAoKkHcDA10zAQFBEQAANwAAAC0AAAAmAAAAKAAA
        ACkAAAApAAAAMAAAADIAAAA0AAAAOAAAADcAAAA5AAAAOQAAADIAAAAoAAAAGQAAAAwAAAAAAAAAAAAA
        AAT5rbEc+Z+hhPqRlP/6goX/+nBy//peYP/3Skr/6jU1/9MgIdpwCwtsGAEBWxgBAV8AAABYAAAATwAA
        AEQAAABHAAAARgAAAEUAAABQAAAAVQAAAFoAAABhAAAAXgAAAF4AAABaAAAATgAAAD4AAAAnAAAAEwAA
        AAAAAAAA+cfKCvm5vT/5rK/D+Z2g//mPkv/6f4P/+m5v//VZW//jQkL/uScosEEKCm4EAABxAAAAdAAA
        AHoAAABxAAAAYgAAAGUAAABgAAAAXgAAAG0AAAB2AAAAfgAAAIYAAACAAAAAfwAAAHYAAABmAAAAUgAA
        ADMAAAAbAAAAAAAAAAD50NMQ+cTHTvm2ueX6qKv/+Zmd//mNj//6fH//9Ghp/95NT++7g4Ob3dnZ2NTU
        1N2bm5vAAAAAkgAAAIu8vLzLn5+fuwAAAHfAwMDHra2txQAAAI3Nzc3kkpKSzDQ0NKPDw8Pa09PT3nJy
        cpUAAABbAAAAOAAAAB8AAAAAAAAAAPrY2xD6z9Jl+cHF9fq0uP/5pan/+Zib//qKjv/0dnn/1FdYts+9
        vab6+vr/6Ojo7vr6+v+Dg4PCAAAAlfr6+v/Q0NDhAAAAhtPT0976+vr/wcHB3Pr6+v9aWlq9+vr6//r6
        +v/6+vr/+vr6/0tLS3AAAAA3AAAAHgAAAAAAAAAA+t/jEPrW2lz6zM/3+r/D//mxtf/5o6b/+ZWY//OE
        h/fFXV940MjImfr6+v8AAAB1+vr6/9HR0eAAAACL+vr6/9HR0eAAAACGAAAAgvr6+v/6+vr/5eXl8TMz
        M6b6+vr/ioqKuDw8PI3a2trXgICAhAAAADUAAAAdAAAAAAAAAAD65OcQ+t3gRvrV2OX6ys3/+r3B//mv
        sv/5oaT/85CTqapbXD3X09OQ+vr6/wAAAFz6+vr/2NjY2QAAAHT6+vr/1dXV3AAAAHsAAAB51dXV3Pr6
        +v+hoaG5oaGhufr6+v8AAABxAAAAaAAAAFoAAABJAAAALQAAABcAAAAAAAAAAPro6wz64+Y3+tzgzfnS
        1v/6x8v/+bu+//mtseP0m55Oo19hIOTg4If6+vr/m5ubbvr6+v/S0tK2AAAATfr6+v/e3t7TAAAAXEtL
        S3H6+vr/+vr6/+7u7ugAAABT+vr6/8bGxqyLi4t6+vr6/6KiomkAAAAfAAAADwAAAAAAAAAAAAAAB/rm
        6iz64uao+tve//nR1f/6xsn8+bm8g/KkqCudYmQO7u7uffr6+v/6+vr/+vr6/319fUT6+vr/+vr6//r6
        +v/6+vr/5+fnyvr6+v/j4+O7+vr6/6enp2bv7+/V+vr6//r6+v/09PTiAAAAIAAAABMAAAAJAAAAAAAA
        AAAAAAAF+urtG/nm6XD64eTx+dnc//nP07P5xMc6+ba5CwAAAAEAAAAEAAAABwAAAAoAAAANAAAAD+bm
        5oHj4+OD4eHhhNjY2HbY2Nh22tradQAAABfk5OSCqqqqMgAAABLh4eFx4eHhcQAAABEAAAANAAAABwAA
        AAMAAAAAAAAAAAAAAAAAAAAD+ensLfnl6Yr53+Kt+dfbRvnN0hcAAAAJAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABAAAAAgAAAAQAAAAFAAAABgAAAAYAAAAFAAAABQAAAAQAAAADAAAAAwAAAAMAAAADAAAAAgAA
        AAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAD56+4T+ensIPnk6B/53uENAAAAAwAAAAMAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA//////8wP//wAAAPgAAAA4AAAAHAAAAD8AAf//wAD//8AAf/+AAAH/gA
        AA+AAAADAAAAAQAAAAEAAAABwAAAAcAAAAHAAAAB4AAAH/AAAAHgAAAA4AAAAMAAAADAAAAAwAAAAMAA
        AADAAAAAwAAAAOAAAAHgOAAD8H////D///8oAAAAEAAAACAAAAABACAAAAAAAEAEAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGUGBhROBgaAOQYGGQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHrAYGFwAAAAl5BgZJYgYG/0oGBnAAAAAJAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF0wYGlL8GBsKnBgZcjQYG/3YGBv9eBgb/RgYGfQAA
        AAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8QYGG+EGBnTPBgb/uQYG4aEGBv+JBgb/cAYG/1kG
        Bv9CBgY0LAYGdRsGBzkNCAcXAAAAAAAAAAD6P0Al+h0dTfoGBrLvBgb/3wYG9MsGBv21Bgb/nwYG/4UG
        Bv9sBgbwVAYG/z4GBv8qBgb/GAgH5AoICDMAAAAG+lteLfo3OPj6GBj/+QYG/+0GBv/cBgb/xwYG/7IG
        Bv+aBgb/gQYG/2gGBv9QBgb/OgYG/ycGBv8VCAj/CQgIMgAAAAj6VFW2+jAx//oUFP/2Bgb/6gYG/9gG
        BvjEBgblrQYG/pUGBv97Bgb/YwYG/0sGBv82Bgb/IgcG/hMICH/5kJML+nFzY/pMTv/6Kyz/+g8Q//UG
        Bv/mBgZ61AYGNr8GBnmoBgZAjgYGUnYGBl5eBgZGSAYGOjIGBiYfBgYSAAAAAPmLjhX6amyE+kZI+/om
        J//6DAv/8gYGcOMGBg/RBgYNAAAAAgAAAAAAAAAHAAAABgAAAAAAAAAAAAAAAAAAAAD5oaMz+oWJ//pl
        Z//6QkP/+iAg2PoICBYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+be6ovmc
        n//6goT/+l9h//o7PGUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB/rK
        zc/5srX/+ZaY//p6fP/6VlgeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAn62d2j+cbJ//qsr//5kpV1AAAACQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAF+uToZPrX2v/5wsXn+qisIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAPrq7Rb54ubI+dPXLgAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAP//AAD8fwAA9H8AAOA/AADAAwAAAAEAAAAAAACAAAAAAAAAAIB/AACB/wAAg/8AAIP/
        AACH/wAAh/8AAI//AAA=
</value>
  </data>
  <data name="$this.ToolTip" xml:space="preserve">
    <value />
  </data>
</root>