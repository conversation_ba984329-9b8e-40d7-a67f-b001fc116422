<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonキャンセル.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonキャンセル.Location" type="System.Drawing.Point, System.Drawing">
    <value>305, 97</value>
  </data>
  <data name="buttonキャンセル.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonキャンセル.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonキャンセル.Text" xml:space="preserve">
    <value>Cencel</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Name" xml:space="preserve">
    <value>buttonキャンセル</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="button置換.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="button置換.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 97</value>
  </data>
  <data name="button置換.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="button置換.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="button置換.Text" xml:space="preserve">
    <value>&amp;Replace</value>
  </data>
  <data name="&gt;&gt;button置換.Name" xml:space="preserve">
    <value>button置換</value>
  </data>
  <data name="&gt;&gt;button置換.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button置換.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button置換.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label説明.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label説明.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 11</value>
  </data>
  <data name="label説明.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 12</value>
  </data>
  <data name="label説明.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label説明.Text" xml:space="preserve">
    <value>Replace selected chips:</value>
  </data>
  <data name="&gt;&gt;label説明.Name" xml:space="preserve">
    <value>label説明</value>
  </data>
  <data name="&gt;&gt;label説明.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label説明.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label説明.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="radioButton表裏反転.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton表裏反転.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 82</value>
  </data>
  <data name="radioButton表裏反転.Size" type="System.Drawing.Size, System.Drawing">
    <value>170, 16</value>
  </data>
  <data name="radioButton表裏反転.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="radioButton表裏反転.Text" xml:space="preserve">
    <value>Re&amp;verse chips (front / back)</value>
  </data>
  <data name="&gt;&gt;radioButton表裏反転.Name" xml:space="preserve">
    <value>radioButton表裏反転</value>
  </data>
  <data name="&gt;&gt;radioButton表裏反転.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton表裏反転.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;radioButton表裏反転.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="radioButton単純置換.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton単純置換.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 35</value>
  </data>
  <data name="radioButton単純置換.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 16</value>
  </data>
  <data name="radioButton単純置換.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="radioButton単純置換.Text" xml:space="preserve">
    <value>Chip &amp;replace</value>
  </data>
  <data name="&gt;&gt;radioButton単純置換.Name" xml:space="preserve">
    <value>radioButton単純置換</value>
  </data>
  <data name="&gt;&gt;radioButton単純置換.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton単純置換.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;radioButton単純置換.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="textBox元番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 57</value>
  </data>
  <data name="textBox元番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 19</value>
  </data>
  <data name="textBox元番号.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBox元番号.Name" xml:space="preserve">
    <value>textBox元番号</value>
  </data>
  <data name="&gt;&gt;textBox元番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox元番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox元番号.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="textBox先番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 57</value>
  </data>
  <data name="textBox先番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 19</value>
  </data>
  <data name="textBox先番号.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;textBox先番号.Name" xml:space="preserve">
    <value>textBox先番号</value>
  </data>
  <data name="&gt;&gt;textBox先番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox先番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox先番号.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>145, 60</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 60</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 12</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>392, 132</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>C置換ダイアログ</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>