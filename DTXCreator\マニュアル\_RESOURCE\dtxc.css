
body {
	color:				black;
	background-color:	white;
	background-image:	none;
	line-height:		150%;
	font-family:		'MS PGothic';
}

table.pageHeader {
	width:				 100%;
	table-layout:		fixed;
	border:				  0px;
	padding:			  4px;
	border-collapse:	collapse;
}

th.pageHeader {
	background-color:	#D2DCFF;
	padding:			4px;
	font-weight:		normal;
	font-size:			small;
	text-align:			left;
	color:				#6644EE;
}

td.pageHeader {
	height:				48px;
	padding:			4px;
	font-weight:		normal;
	font-size:			large;
	text-align:			left;
	background-image:	url('images/headback.bmp');
	color:				#4466FF;
	font-family:		'HGPSoeiKakugothicUB,MS PGothic'
}

b {
	font-weight:		bold;
	font-family:		'HGPGothicE,MS PGothic';
}

h1 {
	font-family:		'HGPSoeiKakugothicUB,MS PGothic';
	font-size:			medium;
	font-weight:		normal;
	color:				#4466FF;
	background-image:	url('images/subheadback.bmp');
	background-repeat:	repeat-x;
	background-position:left bottom;
}

h2 {
	font-family:		'HGPSoeiKakugothicUB,MS PGothic';
	font-size:			medium;
	font-weight:		normal;
	color:				#4444FF;
}

.image_desc {
	font-family:		'MS PGothic';
	font-size:			x-small;
}

img {
	margin:				10px;
}