<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Language" content="en">
<link rel="stylesheet" href="../dtxc.css" type="text/css">
<title>DTXCreator Reference Manual</title>
</head>

<body>

<p>
<table class="pageHeader">
<tr><th class="pageHeader">Miscellaneous</th></tr>
<tr><td class="pageHeader">Options</td></tr>
</table>
<p>Click [Tool(<u>T</u>)] → [Option(<u>O</u>)] in the menubar to show Options dialog.</p>
<p><img border="0" src="images/options_e_.png">
<img border="0" src="images/options-lanes_e_.png"></p>
<blockquote>
<p>General</p>
	<p><a href="#focus">Auto focus</a><br>
	<a href="#recent">Most recently used files list</a><br>
	<a href="#nopreviewBGM">Don't play BGM preview automatically whe WAV clicked</a><br>
	<a href="#playonclick">Play sound when WAV chip is placed</a><br clear="all">
	</p>
<p>Lanes</p>
</blockquote>


<h2><a name="focus"></a>Auto focus</h2>
<p>Check this box to to switch the focus between the edit tab and score region automatically.</p>

<h2><a name="recent"></a>Most recently used files list</h2>
<p>Check this box to show
&quot;Recent files&quot; you edited in the [File(<u>F</u>)] menu.
You can specify a number of recent files, too.</p>


<h2><a name="nopreviewBGM"></a>Don't play BGM preview automatically whe WAV clicked</h2>
<p>If you enables [preview] feature,
the preview sound is automatically played when you click each WAV cell in the [WAV] tab.
However, if you checked this [Don't play BGM preview automatically whe WAV clicked] box,
the BGM files are not previewed regardless of [preview] settings
(except you click [Preview] button in the Sound property etc).
</p>


<h2><a name="playonclick"></a>Play sound when WAV chip is placed</h2>
<p>To play sound when you set WAV chip to the score,
enable this checkbox.
</p>

</body>

</html>