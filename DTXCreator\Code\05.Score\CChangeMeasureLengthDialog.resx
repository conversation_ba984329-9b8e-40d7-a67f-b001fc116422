<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="numericUpDown小節長の倍率.Font" type="System.Drawing.Font, System.Drawing">
    <value>MS UI Gothic, 14.25pt</value>
  </data>
  <data name="numericUpDown小節長の倍率.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 35</value>
  </data>
  <data name="numericUpDown小節長の倍率.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 26</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="numericUpDown小節長の倍率.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;numericUpDown小節長の倍率.Name" xml:space="preserve">
    <value>numericUpDown小節長の倍率</value>
  </data>
  <data name="&gt;&gt;numericUpDown小節長の倍率.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDown小節長の倍率.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDown小節長の倍率.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 97</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonキャンセル.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonキャンセル.Location" type="System.Drawing.Point, System.Drawing">
    <value>253, 97</value>
  </data>
  <data name="buttonキャンセル.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonキャンセル.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonキャンセル.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Name" xml:space="preserve">
    <value>buttonキャンセル</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="textBox小節番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>110, 14</value>
  </data>
  <data name="textBox小節番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="textBox小節番号.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;textBox小節番号.Name" xml:space="preserve">
    <value>textBox小節番号</value>
  </data>
  <data name="&gt;&gt;textBox小節番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox小節番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox小節番号.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkBox後続設定.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox後続設定.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 67</value>
  </data>
  <data name="checkBox後続設定.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 16</value>
  </data>
  <data name="checkBox後続設定.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="checkBox後続設定.Text" xml:space="preserve">
    <value>Change all &amp;following bar scales</value>
  </data>
  <data name="&gt;&gt;checkBox後続設定.Name" xml:space="preserve">
    <value>checkBox後続設定</value>
  </data>
  <data name="&gt;&gt;checkBox後続設定.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox後続設定.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBox後続設定.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label小節長倍率.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label小節長倍率.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 43</value>
  </data>
  <data name="label小節長倍率.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 12</value>
  </data>
  <data name="label小節長倍率.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label小節長倍率.Text" xml:space="preserve">
    <value>&amp;Scale
</value>
  </data>
  <data name="&gt;&gt;label小節長倍率.Name" xml:space="preserve">
    <value>label小節長倍率</value>
  </data>
  <data name="&gt;&gt;label小節長倍率.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label小節長倍率.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label小節長倍率.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label小節番号.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label小節番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 14</value>
  </data>
  <data name="label小節番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 12</value>
  </data>
  <data name="label小節番号.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label小節番号.Text" xml:space="preserve">
    <value>Bar number</value>
  </data>
  <data name="&gt;&gt;label小節番号.Name" xml:space="preserve">
    <value>label小節番号</value>
  </data>
  <data name="&gt;&gt;label小節番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label小節番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label小節番号.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>340, 132</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Change bar length</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>C小節長変更ダイアログ</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>