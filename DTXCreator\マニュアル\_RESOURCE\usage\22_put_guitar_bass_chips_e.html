<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Language" content="en">
<link rel="stylesheet" href="../dtxc.css" type="text/css">
<title>DTXCreator Reference Manual</title>
</head>

<body>

<p>
<table class="pageHeader">
<tr><th class="pageHeader">Basic operations</th></tr>
<tr><td class="pageHeader">Put chips for the guitar/bass</td></tr>
</table>
</p>
<ul>
	<li><a href="#lanes">Voice lane and Pattern lanes</a></li>
</ul>


<h1><a name="lanes"></a>Voice lane and Patter lanes</h1>
<p><img src="images/dtxc_gtr_lane1_e_.png" align="left" style="margin-right: 30"><br>
You can make Guitar/Bass scores using two type of lanes ... &quot;Voice lane&quot; and &quot;Pattern lanes&quot;.
</p>

<h2>Voice lane</h2>
<p>
The lane to specify the sound chip which will be playbacked.
The usage is the same as the drums lanes.
</p>

<h2>Pattern lanes</h2>
<p>The lane to specify RGB patterns.
<br>
To specify <b>OPEN</b> pattern, do <b>Ctrl + left click</b>.
</p>
<br clear="all">
<br>

<h3>Remarks</h3>
If you put some chip on the voice lane without spcifing any pattern lanes,
OPEN pattern will be automatically specified.
</p>


</body>

</html>