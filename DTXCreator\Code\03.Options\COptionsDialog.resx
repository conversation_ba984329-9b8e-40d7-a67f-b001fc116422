<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox_EditMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 122</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pictureBox_EditMode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="pictureBox_EditMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 48</value>
  </data>
  <data name="pictureBox_EditMode.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pictureBox_EditMode.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;pictureBox_EditMode.Name" xml:space="preserve">
    <value>pictureBox_EditMode</value>
  </data>
  <data name="&gt;&gt;pictureBox_EditMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox_EditMode.Parent" xml:space="preserve">
    <value>groupBoxDefaultOperationMode</value>
  </data>
  <data name="&gt;&gt;pictureBox_EditMode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pictureBox_SelectMode.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pictureBox_SelectMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 57</value>
  </data>
  <data name="pictureBox_SelectMode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="pictureBox_SelectMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 48</value>
  </data>
  <data name="pictureBox_SelectMode.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="pictureBox_SelectMode.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;pictureBox_SelectMode.Name" xml:space="preserve">
    <value>pictureBox_SelectMode</value>
  </data>
  <data name="&gt;&gt;pictureBox_SelectMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox_SelectMode.Parent" xml:space="preserve">
    <value>groupBoxDefaultOperationMode</value>
  </data>
  <data name="&gt;&gt;pictureBox_SelectMode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton_EditMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 125</value>
  </data>
  <data name="radioButton_EditMode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_EditMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>329, 74</value>
  </data>
  <data name="radioButton_EditMode.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;radioButton_EditMode.Name" xml:space="preserve">
    <value>radioButton_EditMode</value>
  </data>
  <data name="&gt;&gt;radioButton_EditMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_EditMode.Parent" xml:space="preserve">
    <value>groupBoxDefaultOperationMode</value>
  </data>
  <data name="&gt;&gt;radioButton_EditMode.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="radioButton_SelectMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 57</value>
  </data>
  <data name="radioButton_SelectMode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_SelectMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>329, 74</value>
  </data>
  <data name="radioButton_SelectMode.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;radioButton_SelectMode.Name" xml:space="preserve">
    <value>radioButton_SelectMode</value>
  </data>
  <data name="&gt;&gt;radioButton_SelectMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_SelectMode.Parent" xml:space="preserve">
    <value>groupBoxDefaultOperationMode</value>
  </data>
  <data name="&gt;&gt;radioButton_SelectMode.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBoxDefaultOperationMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 293</value>
  </data>
  <data name="groupBoxDefaultOperationMode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBoxDefaultOperationMode.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBoxDefaultOperationMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>538, 211</value>
  </data>
  <data name="groupBoxDefaultOperationMode.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="groupBoxDefaultOperationMode.Text" xml:space="preserve">
    <value>Initial Operation Mode</value>
  </data>
  <data name="&gt;&gt;groupBoxDefaultOperationMode.Name" xml:space="preserve">
    <value>groupBoxDefaultOperationMode</value>
  </data>
  <data name="&gt;&gt;groupBoxDefaultOperationMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxDefaultOperationMode.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;groupBoxDefaultOperationMode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 225</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>566, 41</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkBoxPlaySoundOnChip.Text" xml:space="preserve">
    <value>&amp;Play sound when WAV chip is placed</value>
  </data>
  <data name="&gt;&gt;checkBoxPlaySoundOnChip.Name" xml:space="preserve">
    <value>checkBoxPlaySoundOnChip</value>
  </data>
  <data name="&gt;&gt;checkBoxPlaySoundOnChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxPlaySoundOnChip.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;checkBoxPlaySoundOnChip.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxPreviewBGM.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxPreviewBGM.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 154</value>
  </data>
  <data name="checkBoxPreviewBGM.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBoxPreviewBGM.Size" type="System.Drawing.Size, System.Drawing">
    <value>847, 41</value>
  </data>
  <data name="checkBoxPreviewBGM.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkBoxPreviewBGM.Text" xml:space="preserve">
    <value>Don't play &amp;BGM preview automatically when WAV clicked</value>
  </data>
  <data name="&gt;&gt;checkBoxPreviewBGM.Name" xml:space="preserve">
    <value>checkBoxPreviewBGM</value>
  </data>
  <data name="&gt;&gt;checkBoxPreviewBGM.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxPreviewBGM.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;checkBoxPreviewBGM.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkBoxオートフォーカス.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxオートフォーカス.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 20</value>
  </data>
  <data name="checkBoxオートフォーカス.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBoxオートフォーカス.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 41</value>
  </data>
  <data name="checkBoxオートフォーカス.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="checkBoxオートフォーカス.Text" xml:space="preserve">
    <value>Auto &amp;focus</value>
  </data>
  <data name="&gt;&gt;checkBoxオートフォーカス.Name" xml:space="preserve">
    <value>checkBoxオートフォーカス</value>
  </data>
  <data name="&gt;&gt;checkBoxオートフォーカス.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxオートフォーカス.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;checkBoxオートフォーカス.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label個まで表示する.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label個まで表示する.Location" type="System.Drawing.Point, System.Drawing">
    <value>618, 88</value>
  </data>
  <data name="label個まで表示する.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 0, 10, 0</value>
  </data>
  <data name="label個まで表示する.Size" type="System.Drawing.Size, System.Drawing">
    <value>178, 37</value>
  </data>
  <data name="label個まで表示する.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label個まで表示する.Text" xml:space="preserve">
    <value>Show up &amp;to</value>
  </data>
  <data name="&gt;&gt;label個まで表示する.Name" xml:space="preserve">
    <value>label個まで表示する</value>
  </data>
  <data name="&gt;&gt;label個まで表示する.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label個まで表示する.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;label個まで表示する.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkBox最近使用したファイル.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox最近使用したファイル.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 85</value>
  </data>
  <data name="checkBox最近使用したファイル.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBox最近使用したファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 41</value>
  </data>
  <data name="checkBox最近使用したファイル.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="checkBox最近使用したファイル.Text" xml:space="preserve">
    <value>Most &amp;recently used files list:</value>
  </data>
  <data name="&gt;&gt;checkBox最近使用したファイル.Name" xml:space="preserve">
    <value>checkBox最近使用したファイル</value>
  </data>
  <data name="&gt;&gt;checkBox最近使用したファイル.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox最近使用したファイル.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;checkBox最近使用したファイル.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="numericUpDown最近使用したファイルの最大表示個数.Location" type="System.Drawing.Point, System.Drawing">
    <value>833, 77</value>
  </data>
  <data name="numericUpDown最近使用したファイルの最大表示個数.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="numericUpDown最近使用したファイルの最大表示個数.Size" type="System.Drawing.Size, System.Drawing">
    <value>177, 44</value>
  </data>
  <data name="numericUpDown最近使用したファイルの最大表示個数.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;numericUpDown最近使用したファイルの最大表示個数.Name" xml:space="preserve">
    <value>numericUpDown最近使用したファイルの最大表示個数</value>
  </data>
  <data name="&gt;&gt;numericUpDown最近使用したファイルの最大表示個数.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDown最近使用したファイルの最大表示個数.Parent" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;numericUpDown最近使用したファイルの最大表示個数.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tabPage全般.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 46</value>
  </data>
  <data name="tabPage全般.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPage全般.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPage全般.Size" type="System.Drawing.Size, System.Drawing">
    <value>1129, 911</value>
  </data>
  <data name="tabPage全般.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPage全般.Text" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="&gt;&gt;tabPage全般.Name" xml:space="preserve">
    <value>tabPage全般</value>
  </data>
  <data name="&gt;&gt;tabPage全般.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage全般.Parent" xml:space="preserve">
    <value>tabControlオプション</value>
  </data>
  <data name="&gt;&gt;tabPage全般.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tabControlオプション.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="labelSelectLanes.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelSelectLanes.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelSelectLanes.Location" type="System.Drawing.Point, System.Drawing">
    <value>421, 23</value>
  </data>
  <data name="labelSelectLanes.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 0, 10, 0</value>
  </data>
  <data name="labelSelectLanes.Size" type="System.Drawing.Size, System.Drawing">
    <value>446, 37</value>
  </data>
  <data name="labelSelectLanes.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelSelectLanes.Text" xml:space="preserve">
    <value>Select Lanes you'd like to use.</value>
  </data>
  <data name="&gt;&gt;labelSelectLanes.Name" xml:space="preserve">
    <value>labelSelectLanes</value>
  </data>
  <data name="&gt;&gt;labelSelectLanes.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelSelectLanes.Parent" xml:space="preserve">
    <value>tabPageLanes</value>
  </data>
  <data name="&gt;&gt;labelSelectLanes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkedListBoxLaneSelectList.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 20</value>
  </data>
  <data name="checkedListBoxLaneSelectList.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkedListBoxLaneSelectList.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 667</value>
  </data>
  <data name="checkedListBoxLaneSelectList.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;checkedListBoxLaneSelectList.Name" xml:space="preserve">
    <value>checkedListBoxLaneSelectList</value>
  </data>
  <data name="&gt;&gt;checkedListBoxLaneSelectList.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckedListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkedListBoxLaneSelectList.Parent" xml:space="preserve">
    <value>tabPageLanes</value>
  </data>
  <data name="&gt;&gt;checkedListBoxLaneSelectList.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPageLanes.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 46</value>
  </data>
  <data name="tabPageLanes.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPageLanes.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPageLanes.Size" type="System.Drawing.Size, System.Drawing">
    <value>1129, 911</value>
  </data>
  <data name="tabPageLanes.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabPageLanes.Text" xml:space="preserve">
    <value>Lanes</value>
  </data>
  <data name="&gt;&gt;tabPageLanes.Name" xml:space="preserve">
    <value>tabPageLanes</value>
  </data>
  <data name="&gt;&gt;tabPageLanes.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPageLanes.Parent" xml:space="preserve">
    <value>tabControlオプション</value>
  </data>
  <data name="&gt;&gt;tabPageLanes.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton_WinSize360.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WinSize360.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 59</value>
  </data>
  <data name="radioButton_WinSize360.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 41</value>
  </data>
  <data name="radioButton_WinSize360.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="radioButton_WinSize360.Text" xml:space="preserve">
    <value>640 x 360</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize360.Name" xml:space="preserve">
    <value>radioButton_WinSize360</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize360.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize360.Parent" xml:space="preserve">
    <value>groupBox_WindowsSizeSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize360.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButton_WinSize540.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WinSize540.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_WinSize540.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 106</value>
  </data>
  <data name="radioButton_WinSize540.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 41</value>
  </data>
  <data name="radioButton_WinSize540.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="radioButton_WinSize540.Text" xml:space="preserve">
    <value>960 x 540</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize540.Name" xml:space="preserve">
    <value>radioButton_WinSize540</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize540.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize540.Parent" xml:space="preserve">
    <value>groupBox_WindowsSizeSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize540.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton_WinSize720.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WinSize720.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_WinSize720.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 153</value>
  </data>
  <data name="radioButton_WinSize720.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 41</value>
  </data>
  <data name="radioButton_WinSize720.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="radioButton_WinSize720.Text" xml:space="preserve">
    <value>1280 x 720</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize720.Name" xml:space="preserve">
    <value>radioButton_WinSize720</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize720.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize720.Parent" xml:space="preserve">
    <value>groupBox_WindowsSizeSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize720.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="radioButton_WinSize1080.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WinSize1080.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_WinSize1080.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 200</value>
  </data>
  <data name="radioButton_WinSize1080.Size" type="System.Drawing.Size, System.Drawing">
    <value>208, 41</value>
  </data>
  <data name="radioButton_WinSize1080.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="radioButton_WinSize1080.Text" xml:space="preserve">
    <value>1920 x 1080</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize1080.Name" xml:space="preserve">
    <value>radioButton_WinSize1080</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize1080.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize1080.Parent" xml:space="preserve">
    <value>groupBox_WindowsSizeSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WinSize1080.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBox_WindowsSizeSettings.Location" type="System.Drawing.Point, System.Drawing">
    <value>595, 179</value>
  </data>
  <data name="groupBox_WindowsSizeSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>429, 295</value>
  </data>
  <data name="groupBox_WindowsSizeSettings.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="groupBox_WindowsSizeSettings.Text" xml:space="preserve">
    <value>Window Size (*)</value>
  </data>
  <data name="&gt;&gt;groupBox_WindowsSizeSettings.Name" xml:space="preserve">
    <value>groupBox_WindowsSizeSettings</value>
  </data>
  <data name="&gt;&gt;groupBox_WindowsSizeSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_WindowsSizeSettings.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;groupBox_WindowsSizeSettings.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="button_DTXViewerPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>991, 57</value>
  </data>
  <data name="button_DTXViewerPath.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="button_DTXViewerPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 65</value>
  </data>
  <data name="button_DTXViewerPath.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="button_DTXViewerPath.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;button_DTXViewerPath.Name" xml:space="preserve">
    <value>button_DTXViewerPath</value>
  </data>
  <data name="&gt;&gt;button_DTXViewerPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button_DTXViewerPath.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;button_DTXViewerPath.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="textBox_DTXViewerPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>358, 57</value>
  </data>
  <data name="textBox_DTXViewerPath.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="textBox_DTXViewerPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>606, 44</value>
  </data>
  <data name="textBox_DTXViewerPath.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;textBox_DTXViewerPath.Name" xml:space="preserve">
    <value>textBox_DTXViewerPath</value>
  </data>
  <data name="&gt;&gt;textBox_DTXViewerPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_DTXViewerPath.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;textBox_DTXViewerPath.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>453, 105</value>
  </data>
  <data name="label1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 0, 10, 0</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>463, 111</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>(You can use it only when
 WASAPI/ASIO is used,
 within PlaySpeed = x0.8 - x1.2)</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox_DTXManiaSettings</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkBox_TimeStretch.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_TimeStretch.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_TimeStretch.Location" type="System.Drawing.Point, System.Drawing">
    <value>402, 57</value>
  </data>
  <data name="checkBox_TimeStretch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBox_TimeStretch.Size" type="System.Drawing.Size, System.Drawing">
    <value>208, 41</value>
  </data>
  <data name="checkBox_TimeStretch.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="checkBox_TimeStretch.Text" xml:space="preserve">
    <value>&amp;TimeStretch</value>
  </data>
  <data name="&gt;&gt;checkBox_TimeStretch.Name" xml:space="preserve">
    <value>checkBox_TimeStretch</value>
  </data>
  <data name="&gt;&gt;checkBox_TimeStretch.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_TimeStretch.Parent" xml:space="preserve">
    <value>groupBox_DTXManiaSettings</value>
  </data>
  <data name="&gt;&gt;checkBox_TimeStretch.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBox_VSyncWait.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_VSyncWait.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_VSyncWait.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 122</value>
  </data>
  <data name="checkBox_VSyncWait.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBox_VSyncWait.Size" type="System.Drawing.Size, System.Drawing">
    <value>193, 41</value>
  </data>
  <data name="checkBox_VSyncWait.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="checkBox_VSyncWait.Text" xml:space="preserve">
    <value>V&amp;SyncWait</value>
  </data>
  <data name="&gt;&gt;checkBox_VSyncWait.Name" xml:space="preserve">
    <value>checkBox_VSyncWait</value>
  </data>
  <data name="&gt;&gt;checkBox_VSyncWait.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_VSyncWait.Parent" xml:space="preserve">
    <value>groupBox_DTXManiaSettings</value>
  </data>
  <data name="&gt;&gt;checkBox_VSyncWait.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkBox_GRmode.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_GRmode.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 57</value>
  </data>
  <data name="checkBox_GRmode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="checkBox_GRmode.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 41</value>
  </data>
  <data name="checkBox_GRmode.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="checkBox_GRmode.Text" xml:space="preserve">
    <value>&amp;GR mode</value>
  </data>
  <data name="&gt;&gt;checkBox_GRmode.Name" xml:space="preserve">
    <value>checkBox_GRmode</value>
  </data>
  <data name="&gt;&gt;checkBox_GRmode.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_GRmode.Parent" xml:space="preserve">
    <value>groupBox_DTXManiaSettings</value>
  </data>
  <data name="&gt;&gt;checkBox_GRmode.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBox_DTXManiaSettings.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 636</value>
  </data>
  <data name="groupBox_DTXManiaSettings.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_DTXManiaSettings.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_DTXManiaSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>991, 225</value>
  </data>
  <data name="groupBox_DTXManiaSettings.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="groupBox_DTXManiaSettings.Text" xml:space="preserve">
    <value>Other DTXManiaGR Settings</value>
  </data>
  <data name="&gt;&gt;groupBox_DTXManiaSettings.Name" xml:space="preserve">
    <value>groupBox_DTXManiaSettings</value>
  </data>
  <data name="&gt;&gt;groupBox_DTXManiaSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_DTXManiaSettings.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;groupBox_DTXManiaSettings.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label_Notice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="label_Notice.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label_Notice.Location" type="System.Drawing.Point, System.Drawing">
    <value>589, 479</value>
  </data>
  <data name="label_Notice.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 0, 10, 0</value>
  </data>
  <data name="label_Notice.Size" type="System.Drawing.Size, System.Drawing">
    <value>389, 148</value>
  </data>
  <data name="label_Notice.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label_Notice.Text" xml:space="preserve">
    <value>(*)
You need to reboot viewer
after changing these
parameters.</value>
  </data>
  <data name="&gt;&gt;label_Notice.Name" xml:space="preserve">
    <value>label_Notice</value>
  </data>
  <data name="&gt;&gt;label_Notice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label_Notice.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;label_Notice.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="radioButton_WASAPI_Shared.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WASAPI_Shared.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_WASAPI_Shared.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 192</value>
  </data>
  <data name="radioButton_WASAPI_Shared.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_WASAPI_Shared.Size" type="System.Drawing.Size, System.Drawing">
    <value>272, 41</value>
  </data>
  <data name="radioButton_WASAPI_Shared.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="radioButton_WASAPI_Shared.Text" xml:space="preserve">
    <value>WASAPI S&amp;hared</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Shared.Name" xml:space="preserve">
    <value>radioButton_WASAPI_Shared</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Shared.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Shared.Parent" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Shared.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButton_DirectSound.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_DirectSound.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 55</value>
  </data>
  <data name="radioButton_DirectSound.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_DirectSound.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 41</value>
  </data>
  <data name="radioButton_DirectSound.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="radioButton_DirectSound.Text" xml:space="preserve">
    <value>&amp;DirectSound</value>
  </data>
  <data name="&gt;&gt;radioButton_DirectSound.Name" xml:space="preserve">
    <value>radioButton_DirectSound</value>
  </data>
  <data name="&gt;&gt;radioButton_DirectSound.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_DirectSound.Parent" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_DirectSound.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 120</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.Size" type="System.Drawing.Size, System.Drawing">
    <value>301, 41</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="radioButton_WASAPI_Exclusive.Text" xml:space="preserve">
    <value>&amp;WASAPI Exclusive</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Exclusive.Name" xml:space="preserve">
    <value>radioButton_WASAPI_Exclusive</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Exclusive.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Exclusive.Parent" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_WASAPI_Exclusive.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comboBox_ASIOdevices.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 328</value>
  </data>
  <data name="comboBox_ASIOdevices.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="comboBox_ASIOdevices.Size" type="System.Drawing.Size, System.Drawing">
    <value>374, 45</value>
  </data>
  <data name="comboBox_ASIOdevices.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;comboBox_ASIOdevices.Name" xml:space="preserve">
    <value>comboBox_ASIOdevices</value>
  </data>
  <data name="&gt;&gt;comboBox_ASIOdevices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBox_ASIOdevices.Parent" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;comboBox_ASIOdevices.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="radioButton_ASIO.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_ASIO.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_ASIO.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 260</value>
  </data>
  <data name="radioButton_ASIO.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_ASIO.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 41</value>
  </data>
  <data name="radioButton_ASIO.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="radioButton_ASIO.Text" xml:space="preserve">
    <value>&amp;ASIO (ASIO devices only)</value>
  </data>
  <data name="&gt;&gt;radioButton_ASIO.Name" xml:space="preserve">
    <value>radioButton_ASIO</value>
  </data>
  <data name="&gt;&gt;radioButton_ASIO.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_ASIO.Parent" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;radioButton_ASIO.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 179</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>536, 438</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="groupBox_SoundDeviceSettings.Text" xml:space="preserve">
    <value>SoundDevice (*)</value>
  </data>
  <data name="&gt;&gt;groupBox_SoundDeviceSettings.Name" xml:space="preserve">
    <value>groupBox_SoundDeviceSettings</value>
  </data>
  <data name="&gt;&gt;groupBox_SoundDeviceSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_SoundDeviceSettings.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;groupBox_SoundDeviceSettings.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="radioButton_UseDTXViewer.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_UseDTXViewer.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 57</value>
  </data>
  <data name="radioButton_UseDTXViewer.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_UseDTXViewer.Size" type="System.Drawing.Size, System.Drawing">
    <value>261, 41</value>
  </data>
  <data name="radioButton_UseDTXViewer.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioButton_UseDTXViewer.Text" xml:space="preserve">
    <value>Use DTX&amp;Viewer</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXViewer.Name" xml:space="preserve">
    <value>radioButton_UseDTXViewer</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXViewer.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXViewer.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXViewer.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 122</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.Size" type="System.Drawing.Size, System.Drawing">
    <value>354, 41</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButton_UseDTXManiaGR.Text" xml:space="preserve">
    <value>Use DTX&amp;ManiaNX.exe</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXManiaGR.Name" xml:space="preserve">
    <value>radioButton_UseDTXManiaGR</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXManiaGR.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXManiaGR.Parent" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;radioButton_UseDTXManiaGR.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="groupBox_SelectViewer.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 9</value>
  </data>
  <data name="groupBox_SelectViewer.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_SelectViewer.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="groupBox_SelectViewer.Size" type="System.Drawing.Size, System.Drawing">
    <value>1099, 879</value>
  </data>
  <data name="groupBox_SelectViewer.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="groupBox_SelectViewer.Text" xml:space="preserve">
    <value>Viewer Settings</value>
  </data>
  <data name="&gt;&gt;groupBox_SelectViewer.Name" xml:space="preserve">
    <value>groupBox_SelectViewer</value>
  </data>
  <data name="&gt;&gt;groupBox_SelectViewer.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_SelectViewer.Parent" xml:space="preserve">
    <value>tabPageViewer</value>
  </data>
  <data name="&gt;&gt;groupBox_SelectViewer.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tabPageViewer.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 46</value>
  </data>
  <data name="tabPageViewer.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPageViewer.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabPageViewer.Size" type="System.Drawing.Size, System.Drawing">
    <value>1129, 911</value>
  </data>
  <data name="tabPageViewer.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tabPageViewer.Text" xml:space="preserve">
    <value>Viewer</value>
  </data>
  <data name="&gt;&gt;tabPageViewer.Name" xml:space="preserve">
    <value>tabPageViewer</value>
  </data>
  <data name="&gt;&gt;tabPageViewer.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPageViewer.Parent" xml:space="preserve">
    <value>tabControlオプション</value>
  </data>
  <data name="&gt;&gt;tabPageViewer.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tabControlオプション.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 18</value>
  </data>
  <data name="tabControlオプション.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="tabControlオプション.Size" type="System.Drawing.Size, System.Drawing">
    <value>1137, 961</value>
  </data>
  <data name="tabControlオプション.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;tabControlオプション.Name" xml:space="preserve">
    <value>tabControlオプション</value>
  </data>
  <data name="&gt;&gt;tabControlオプション.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControlオプション.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControlオプション.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="button1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="button1.Location" type="System.Drawing.Point, System.Drawing">
    <value>937, 983</value>
  </data>
  <data name="button1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="button1.Size" type="System.Drawing.Size, System.Drawing">
    <value>238, 71</value>
  </data>
  <data name="button1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="button1.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;button1.Name" xml:space="preserve">
    <value>button1</value>
  </data>
  <data name="&gt;&gt;button1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>679, 983</value>
  </data>
  <data name="buttonOK.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>238, 71</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="openFileDialog_DTXViewerPath.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>15, 19</value>
  </metadata>
  <data name="openFileDialog_DTXViewerPath.Filter" xml:space="preserve">
    <value>Executable files|*.exe</value>
  </data>
  <data name="openFileDialog_DTXViewerPath.Title" xml:space="preserve">
    <value>DTXViewer location</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>19, 37</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1213, 1061</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>10, 9, 10, 9</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="&gt;&gt;openFileDialog_DTXViewerPath.Name" xml:space="preserve">
    <value>openFileDialog_DTXViewerPath</value>
  </data>
  <data name="&gt;&gt;openFileDialog_DTXViewerPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.OpenFileDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>COptionsDialog</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>