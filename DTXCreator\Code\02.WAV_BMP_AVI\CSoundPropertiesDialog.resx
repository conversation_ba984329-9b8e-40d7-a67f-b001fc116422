<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="textBoxラベル.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 40</value>
  </data>
  <data name="textBoxラベル.Size" type="System.Drawing.Size, System.Drawing">
    <value>294, 19</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBoxラベル.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textBoxラベル.ToolTip" xml:space="preserve">
    <value>You can give any label name to WAV.</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Name" xml:space="preserve">
    <value>textBoxラベル</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="labelラベル.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelラベル.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 43</value>
  </data>
  <data name="labelラベル.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 12</value>
  </data>
  <data name="labelラベル.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="labelラベル.Text" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="labelラベル.ToolTip" xml:space="preserve">
    <value>You can give any label name to WAV.</value>
  </data>
  <data name="&gt;&gt;labelラベル.Name" xml:space="preserve">
    <value>labelラベル</value>
  </data>
  <data name="&gt;&gt;labelラベル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelラベル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelラベル.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="textBoxファイル.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 65</value>
  </data>
  <data name="textBoxファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="textBoxファイル.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="textBoxファイル.ToolTip" xml:space="preserve">
    <value>File path of this WAV.</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Name" xml:space="preserve">
    <value>textBoxファイル</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="labelファイル.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelファイル.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 68</value>
  </data>
  <data name="labelファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 12</value>
  </data>
  <data name="labelファイル.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="labelファイル.Text" xml:space="preserve">
    <value>&amp;File</value>
  </data>
  <data name="labelファイル.ToolTip" xml:space="preserve">
    <value>File path of this WAV.</value>
  </data>
  <data name="&gt;&gt;labelファイル.Name" xml:space="preserve">
    <value>labelファイル</value>
  </data>
  <data name="&gt;&gt;labelファイル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelファイル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelファイル.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="label音量.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label音量.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 123</value>
  </data>
  <data name="label音量.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 12</value>
  </data>
  <data name="label音量.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="label音量.Text" xml:space="preserve">
    <value>&amp;Volume</value>
  </data>
  <data name="label音量.ToolTip" xml:space="preserve">
    <value>Volume of this WAV</value>
  </data>
  <data name="&gt;&gt;label音量.Name" xml:space="preserve">
    <value>label音量</value>
  </data>
  <data name="&gt;&gt;label音量.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label音量.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label音量.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="label位置.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label位置.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 159</value>
  </data>
  <data name="label位置.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 12</value>
  </data>
  <data name="label位置.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label位置.Text" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="label位置.ToolTip" xml:space="preserve">
    <value>Position of this WAV</value>
  </data>
  <data name="&gt;&gt;label位置.Name" xml:space="preserve">
    <value>label位置</value>
  </data>
  <data name="&gt;&gt;label位置.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label位置.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label位置.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="button参照.Location" type="System.Drawing.Point, System.Drawing">
    <value>292, 61</value>
  </data>
  <data name="button参照.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 23</value>
  </data>
  <data name="button参照.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="button参照.Text" xml:space="preserve">
    <value>&amp;Browse...</value>
  </data>
  <data name="button参照.ToolTip" xml:space="preserve">
    <value>Select file</value>
  </data>
  <data name="&gt;&gt;button参照.Name" xml:space="preserve">
    <value>button参照</value>
  </data>
  <data name="&gt;&gt;button参照.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button参照.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button参照.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="hScrollBar音量.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 120</value>
  </data>
  <data name="hScrollBar音量.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 17</value>
  </data>
  <data name="hScrollBar音量.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="hScrollBar音量.ToolTip" xml:space="preserve">
    <value>Volume of this WAV</value>
  </data>
  <data name="&gt;&gt;hScrollBar音量.Name" xml:space="preserve">
    <value>hScrollBar音量</value>
  </data>
  <data name="&gt;&gt;hScrollBar音量.Type" xml:space="preserve">
    <value>System.Windows.Forms.HScrollBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hScrollBar音量.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;hScrollBar音量.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="textBox音量.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 120</value>
  </data>
  <data name="textBox音量.MaxLength" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="textBox音量.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 19</value>
  </data>
  <data name="textBox音量.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="textBox音量.Text" xml:space="preserve">
    <value>100</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBox音量.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="textBox音量.ToolTip" xml:space="preserve">
    <value>Volume of this WAV</value>
  </data>
  <data name="textBox音量.WordWrap" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textBox音量.Name" xml:space="preserve">
    <value>textBox音量</value>
  </data>
  <data name="&gt;&gt;textBox音量.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox音量.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox音量.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="textBox位置.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 156</value>
  </data>
  <data name="textBox位置.MaxLength" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="textBox位置.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 19</value>
  </data>
  <data name="textBox位置.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="textBox位置.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBox位置.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="textBox位置.ToolTip" xml:space="preserve">
    <value>Position of this WAV</value>
  </data>
  <data name="textBox位置.WordWrap" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textBox位置.Name" xml:space="preserve">
    <value>textBox位置</value>
  </data>
  <data name="&gt;&gt;textBox位置.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox位置.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBox位置.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="hScrollBar位置.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 158</value>
  </data>
  <data name="hScrollBar位置.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 17</value>
  </data>
  <data name="hScrollBar位置.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="hScrollBar位置.ToolTip" xml:space="preserve">
    <value>Position of this WAV</value>
  </data>
  <data name="&gt;&gt;hScrollBar位置.Name" xml:space="preserve">
    <value>hScrollBar位置</value>
  </data>
  <data name="&gt;&gt;hScrollBar位置.Type" xml:space="preserve">
    <value>System.Windows.Forms.HScrollBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;hScrollBar位置.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;hScrollBar位置.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="button背景色.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 9</value>
  </data>
  <data name="button背景色.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 23</value>
  </data>
  <data name="button背景色.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="button背景色.Text" xml:space="preserve">
    <value>BGcol.</value>
  </data>
  <data name="button背景色.ToolTip" xml:space="preserve">
    <value>Background color of this row</value>
  </data>
  <data name="&gt;&gt;button背景色.Name" xml:space="preserve">
    <value>button背景色</value>
  </data>
  <data name="&gt;&gt;button背景色.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button背景色.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button背景色.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="button文字色.Location" type="System.Drawing.Point, System.Drawing">
    <value>190, 9</value>
  </data>
  <data name="button文字色.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 23</value>
  </data>
  <data name="button文字色.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="button文字色.Text" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="button文字色.ToolTip" xml:space="preserve">
    <value>Text color of this row</value>
  </data>
  <data name="&gt;&gt;button文字色.Name" xml:space="preserve">
    <value>button文字色</value>
  </data>
  <data name="&gt;&gt;button文字色.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button文字色.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button文字色.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="button標準色に戻す.Location" type="System.Drawing.Point, System.Drawing">
    <value>247, 9</value>
  </data>
  <data name="button標準色に戻す.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 23</value>
  </data>
  <data name="button標準色に戻す.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="button標準色に戻す.Text" xml:space="preserve">
    <value>Reset colors</value>
  </data>
  <data name="button標準色に戻す.ToolTip" xml:space="preserve">
    <value>Reset to default colors</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Name" xml:space="preserve">
    <value>button標準色に戻す</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="button試聴.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 202</value>
  </data>
  <data name="button試聴.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="button試聴.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="button試聴.Text" xml:space="preserve">
    <value>&amp;Preview</value>
  </data>
  <data name="button試聴.ToolTip" xml:space="preserve">
    <value>Play sound file</value>
  </data>
  <data name="&gt;&gt;button試聴.Name" xml:space="preserve">
    <value>button試聴</value>
  </data>
  <data name="&gt;&gt;button試聴.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button試聴.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button試聴.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxBGM.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxBGM.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 90</value>
  </data>
  <data name="checkBoxBGM.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 16</value>
  </data>
  <data name="checkBoxBGM.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="checkBoxBGM.Text" xml:space="preserve">
    <value>Use this sound as BGM</value>
  </data>
  <data name="checkBoxBGM.ToolTip" xml:space="preserve">
    <value>If this is checked, it means you use this WAV for BGM.
This switch is used when you select "Play BGM from current part".</value>
  </data>
  <data name="&gt;&gt;checkBoxBGM.Name" xml:space="preserve">
    <value>checkBoxBGM</value>
  </data>
  <data name="&gt;&gt;checkBoxBGM.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxBGM.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxBGM.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 202</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="buttonキャンセル.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 202</value>
  </data>
  <data name="buttonキャンセル.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonキャンセル.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonキャンセル.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Name" xml:space="preserve">
    <value>buttonキャンセル</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="textBoxWAV番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>69, 11</value>
  </data>
  <data name="textBoxWAV番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 19</value>
  </data>
  <data name="textBoxWAV番号.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="textBoxWAV番号.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;textBoxWAV番号.Name" xml:space="preserve">
    <value>textBoxWAV番号</value>
  </data>
  <data name="&gt;&gt;textBoxWAV番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxWAV番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxWAV番号.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelWAV番号.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelWAV番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 14</value>
  </data>
  <data name="labelWAV番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 12</value>
  </data>
  <data name="labelWAV番号.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="labelWAV番号.Text" xml:space="preserve">
    <value>WAV No.</value>
  </data>
  <data name="&gt;&gt;labelWAV番号.Name" xml:space="preserve">
    <value>labelWAV番号</value>
  </data>
  <data name="&gt;&gt;labelWAV番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelWAV番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelWAV番号.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label音量無音.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label音量無音.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 108</value>
  </data>
  <data name="label音量無音.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 12</value>
  </data>
  <data name="label音量無音.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="label音量無音.Text" xml:space="preserve">
    <value>Silent</value>
  </data>
  <data name="label音量無音.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label音量無音.Name" xml:space="preserve">
    <value>label音量無音</value>
  </data>
  <data name="&gt;&gt;label音量無音.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label音量無音.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label音量無音.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label位置左.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label位置左.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 142</value>
  </data>
  <data name="label位置左.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 12</value>
  </data>
  <data name="label位置左.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="label位置左.Text" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="label位置左.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label位置左.Name" xml:space="preserve">
    <value>label位置左</value>
  </data>
  <data name="&gt;&gt;label位置左.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label位置左.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label位置左.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="labe音量原音.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labe音量原音.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 108</value>
  </data>
  <data name="labe音量原音.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 12</value>
  </data>
  <data name="labe音量原音.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="labe音量原音.Text" xml:space="preserve">
    <value>Original</value>
  </data>
  <data name="labe音量原音.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labe音量原音.Name" xml:space="preserve">
    <value>labe音量原音</value>
  </data>
  <data name="&gt;&gt;labe音量原音.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labe音量原音.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labe音量原音.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label位置右.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label位置右.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 142</value>
  </data>
  <data name="label位置右.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 12</value>
  </data>
  <data name="label位置右.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="label位置右.Text" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="label位置右.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label位置右.Name" xml:space="preserve">
    <value>label位置右</value>
  </data>
  <data name="&gt;&gt;label位置右.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label位置右.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label位置右.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label位置中央.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label位置中央.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 142</value>
  </data>
  <data name="label位置中央.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 12</value>
  </data>
  <data name="label位置中央.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="label位置中央.Text" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="label位置中央.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label位置中央.Name" xml:space="preserve">
    <value>label位置中央</value>
  </data>
  <data name="&gt;&gt;label位置中央.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label位置中央.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label位置中央.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>378, 237</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sound property</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>Cサウンドプロパティダイアログ</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>