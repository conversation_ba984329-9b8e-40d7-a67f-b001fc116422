﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

// 注意：
// SlimDX.DirectInput.Key は、DIK_ とは数値が異なる。
// SharpDX.DirectInput.Key は、DIK_ と同じ値である。

namespace SlimDX.DirectInput
{
	public enum Key
	{
		D0 = 0,
		D1 = 1,
		D2 = 2,
		D3 = 3,
		D4 = 4,
		D5 = 5,
		D6 = 6,
		D7 = 7,
		D8 = 8,
		D9 = 9,
		A = 10,
		B = 11,
		C = 12,
		D = 13,
		E = 14,
		F = 15,
		G = 16,
		H = 17,
		I = 18,
		J = 19,
		K = 20,
		L = 21,
		M = 22,
		N = 23,
		O = 24,
		P = 25,
		Q = 26,
		R = 27,
		S = 28,
		T = 29,
		U = 30,
		V = 31,
		W = 32,
		X = 33,
		Y = 34,
		Z = 35,
		AbntC1 = 36,
		AbntC2 = 37,
		Apostrophe = 38,
		Applications = 39,
		AT = 40,
		AX = 41,
		Backspace = 42,
		Backslash = 43,
		Calculator = 44,
		CapsLock = 45,
		Colon = 46,
		Comma = 47,
		Convert = 48,
		Delete = 49,
		DownArrow = 50,
		End = 51,
		Equals = 52,
		Escape = 53,
		F1 = 54,
		F2 = 55,
		F3 = 56,
		F4 = 57,
		F5 = 58,
		F6 = 59,
		F7 = 60,
		F8 = 61,
		F9 = 62,
		F10 = 63,
		F11 = 64,
		F12 = 65,
		F13 = 66,
		F14 = 67,
		F15 = 68,
		Grave = 69,
		Home = 70,
		Insert = 71,
		Kana = 72,
		Kanji = 73,
		LeftBracket = 74,
		LeftControl = 75,
		LeftArrow = 76,
		LeftAlt = 77,
		LeftShift = 78,
		LeftWindowsKey = 79,
		Mail = 80,
		MediaSelect = 81,
		MediaStop = 82,
		Minus = 83,
		Mute = 84,
		MyComputer = 85,
		NextTrack = 86,
		NoConvert = 87,
		NumberLock = 88,
		NumberPad0 = 89,
		NumberPad1 = 90,
		NumberPad2 = 91,
		NumberPad3 = 92,
		NumberPad4 = 93,
		NumberPad5 = 94,
		NumberPad6 = 95,
		NumberPad7 = 96,
		NumberPad8 = 97,
		NumberPad9 = 98,
		NumberPadComma = 99,
		NumberPadEnter = 100,
		NumberPadEquals = 101,
		NumberPadMinus = 102,
		NumberPadPeriod = 103,
		NumberPadPlus = 104,
		NumberPadSlash = 105,
		NumberPadStar = 106,
		Oem102 = 107,
		PageDown = 108,
		PageUp = 109,
		Pause = 110,
		Period = 111,
		PlayPause = 112,
		Power = 113,
		PreviousTrack = 114,
		RightBracket = 115,
		RightControl = 116,
		Return = 117,
		RightArrow = 118,
		RightAlt = 119,
		RightShift = 120,
		RightWindowsKey = 121,
		ScrollLock = 122,
		Semicolon = 123,
		Slash = 124,
		Sleep = 125,
		Space = 126,
		Stop = 127,
		PrintScreen = 128,
		Tab = 129,
		Underline = 130,
		Unlabeled = 131,
		UpArrow = 132,
		VolumeDown = 133,
		VolumeUp = 134,
		Wake = 135,
		WebBack = 136,
		WebFavorites = 137,
		WebForward = 138,
		WebHome = 139,
		WebRefresh = 140,
		WebSearch = 141,
		WebStop = 142,
		Yen = 143,
		Unknown = 144
	}
}
