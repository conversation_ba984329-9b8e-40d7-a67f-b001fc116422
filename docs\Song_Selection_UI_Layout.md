# Song Selection UI Layout Documentation

## Overview

This document provides detailed information about the visual layout, positioning, and graphics resources used in DTXMania's song selection screen. The layout is designed for a **1280x720** resolution and uses a sophisticated multi-panel arrangement.

**Related Documents:**
- [Song Selection UI Implementation](Song_Selection_UI_Implementation.md) - Technical implementation details

## Screen Resolution and Dimensions

### Base Resolution
- **Screen Width**: 1280 pixels
- **Screen Height**: 720 pixels
- **Coordinate System**: Top-left origin (0,0)

### Key Layout Constants
```cpp
// Preview panel dimensions
PREVIEW_W = 0xcc (204 pixels)
PREVIEW_H = 0x10d (269 pixels)

// Panel dimensions
PANEL_W = 0x116 (278 pixels)  
PANEL_H = 0x1a (26 pixels)

// Score display
SCORE_W = 12 pixels
SCORE_H = 0x18 (24 pixels)
```

## Main Layout Structure

### Background Layer
- **Background Image**: `Graphics\5_background.jpg`
- **Position**: (0, 0) - Full screen
- **Size**: 1280x720

### Header Panel
- **Texture**: `Graphics\5_header panel.png`
- **Position**: (0, y) where y animates from negative to 0
- **Animation**: Slides down on stage entry
- **Contains**: Top navigation and title information

### Footer Panel  
- **Texture**: `Graphics\5_footer panel.png`
- **Position**: (0, 720 - panel_height)
- **Fixed**: Bottom of screen
- **Contains**: Bottom status information

## Song List Area

### Song Bar Positioning
The song list displays **13 bars** simultaneously with precise positioning:

**Bar Base Coordinates** (from `ptバーの基本座標`):
```
Bar 0:  (708, 5)    - Top bar
Bar 1:  (626, 56)
Bar 2:  (578, 107)
Bar 3:  (546, 158)
Bar 4:  (528, 209)
Bar 5:  (464, 270)  - Selection focus area
Bar 6:  (548, 362)  - Center/Selected bar
Bar 7:  (578, 413)
Bar 8:  (624, 464)
Bar 9:  (686, 515)
Bar 10: (788, 566)
Bar 11: (996, 617)
Bar 12: (1280, 668) - Bottom bar
```

**Selected Row**: Bar index 5 (6th bar) serves as the selection focus point.

### Song Bar Graphics
- **Score Bar**: `Graphics\5_bar score.png` / `Graphics\5_bar score selected.png`
- **Box Bar**: `Graphics\5_bar box.png` / `Graphics\5_bar box selected.png`
- **Other Bar**: `Graphics\5_bar other.png` / `Graphics\5_bar other selected.png`

### Song Bar Components
Each bar contains:
- **Song title texture** (dynamically generated)
- **Preview image** (204x269 pixels max)
- **Clear lamp indicators** (5 difficulty levels)
- **Skill value display**

<function_calls>
<invoke name="view">
<parameter name="path">DTXMania/Code/Stage/05.SongSelection/CActSelectSongList.cs
