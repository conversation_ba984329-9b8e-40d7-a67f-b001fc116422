<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Language" content="en">
<link rel="stylesheet" href="../dtxc.css" type="text/css">
<title>DTXCreator Reference Manual</title>
</head>

<body>

<p>
<table class="pageHeader">
<tr><th class="pageHeader">Basic operations</th></tr>
<tr><td class="pageHeader">Testplay by DTXViewer</td></tr>
</table>
<p>After you make DTX data partly, you should check it by playing on DTXViewer.</p>
<h1>Play from the beginning</h1>
<p>Click [Play(<u>P)</u>] - [Play from top] in the menu bar or click <img border="0" src="images/playfromthebeginning.png" width="16" height="16" style="margin: 0"> button in the toolbar.
It starts DTXViewer and start playing the editing score from the beginning.
</p>

<h1>Playback from the current position</h1>
<p>Click [Play(<u>P</u>)] - [Play from current part] in the menu bar or click<img border="0" src="images/DataContainer_MoveNextHS.png" width="16" height="16" style="margin: 0"> button in the toolbar.
It starts DTXViewer and start playing from the bar which is shown in the DTXCreator.</p>

<h2>Changing playabck speed</h2>
<p>You can change the playback speed from toolbar.</p>
<blockquote>
	<p><img border="0" src="images/dtxvplayspeed_e_.png" width="239" height="250"></p>
</blockquote>

<h1>Playback only BGM from the current position</h1>
<p>Click [Play(<u>P</u>)] - [Play BGM from current part] in the menubar or click<img border="0" src="images/DataContainer_NewRecordHS.png" width="16" height="16" style="margin: 0"> button in the toolbar.
It starts DTXViewer and start playing BGM from the bar which is shown in the DTXCreator.
<br>
The BGM means which is checked &quot;Use this sound as BGM&quot; in the Sound property.
</p>

<h1>Stop playing</h1>
<p>Click  [Play(<u>P</u>)] - [Stop] in the menubar or click<img border="0" src="images/StopHS.png" width="16" height="16" style="margin: 0"> button in the toolbar. It stops playing in DTXViewer.
</p>

<h3>Remarks</h3>
<ul>
	<li>To use these features, you have to copy DTXV.exe in the same folder where DTXCreator.exe is.</li>
</ul>
<p>　</p>


</body>

</html>