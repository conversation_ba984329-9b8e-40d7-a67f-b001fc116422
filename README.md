## What is DTXManiaNX?
DTXManiaNX is a program that replicates gameplay from <PERSON><PERSON><PERSON>'s music video game, Gitadora - Drummania/GuitarFreaks. This project was forked from the DTXManiaXG verK SVN repository. It processes DTX files (including older formats such as BMS/BME or GDA/G2D) and allows playing of custom created charts with a use of a game, keyboard or MIDI controller.

For more information regarding creation of DTX files and its data formats, do visit the original [DTXMania Wiki](https://osdn.net/projects/dtxmania/wiki/DTX%20data%20format). Various video tutorials are available from [APPROVED DTX Gaming's YouTube page](https://youtu.be/9GlSk62pgGw) or [
Furukon Rhythm Gaming's YouTube page](https://www.youtube.com/playlist?list=PLj22ny7-DS2V-l0pWLhp8cLRYLF3jskCs).

## Original and Ongoing Forks
* [DTXMania](https://osdn.net/projects/dtxmania) (yyagi)

https://osdn.net/projects/dtxmania

* [DTXMania2](https://dtxmania.net) ([ＦＲＯＭ](https://github.com/DTXMania))

https://dtxmania.net

* [DTXMania AL](http://senamih.com/dtxal) (Sena)

http://senamih.com/dtxal

* [DTXManiaXG verK](https://osdn.net/projects/dtxmaniaxg-verk) ([kairera0467](https://github.com/kairera0467))

https://osdn.net/projects/dtxmaniaxg-verk

## Installation
1. Download the [latest release](https://github.com/limyz/DTXmaniaXG/releases) of DTXMania and extract it to a location of your choice

2. Download and install the [.NET Framework 4.7.1](https://dotnet.microsoft.com/download/dotnet-framework/net471) (if prompted)

3. Download and install the [DirectX End-User Runtime (DirectX v9.0c)](https://www.microsoft.com/en-us/download/details.aspx?displaylang=en&id=35)

## Community Support
For additional help or support, ask away in DTXMania on Discord! 
[https://discord.gg/ST5MWHe](https://discord.gg/ST5MWHe)
