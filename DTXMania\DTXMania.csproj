﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{81BEC512-8074-4BD1-8A3C-AC73BC7BF846}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DTXMania</RootNamespace>
    <AssemblyName>DTXManiaNX</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationIcon>dtx.ico</ApplicationIcon>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <ApplicationManifest>DTXManiaNX.exe.manifest</ApplicationManifest>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Runtime\</OutputPath>
    <DefineConstants>TRACE;TEST_ENGLISH_ TEST_Direct3D9Ex_</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>0219</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>..\Runtime\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>0219</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowedReferenceRelatedFileExtensions>none</AllowedReferenceRelatedFileExtensions>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Runtime\</OutputPath>
    <DefineConstants>TRACE;TEST_ENGLISH_ TEST_Direct3D9Ex_</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>0219</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\Runtime\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>0219</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DirectShowLib-2005">
      <HintPath>..\Runtime\dll\DirectShowLib-2005.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DiscordRPC, Version=1.0.169.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DiscordRichPresence.1.0.169\lib\net35\DiscordRPC.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX">
      <HintPath>..\Runtime\dll\SharpDX.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Animation">
      <HintPath>..\Runtime\dll\SharpDX.Animation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Direct3D9">
      <HintPath>..\Runtime\dll\SharpDX.Direct3D9.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DirectInput">
      <HintPath>..\Runtime\dll\SharpDX.DirectInput.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DirectSound">
      <HintPath>..\Runtime\dll\SharpDX.DirectSound.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.DXGI">
      <HintPath>..\Runtime\dll\SharpDX.DXGI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SharpDX.Mathematics">
      <HintPath>..\Runtime\dll\SharpDX.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Code\App\CCommandParse.cs" />
    <Compile Include="Code\App\CDiscordRichPresence.cs" />
    <Compile Include="Code\App\CDTX2WAVmode.cs" />
    <Compile Include="Code\App\CDTXRichPresence.cs" />
    <Compile Include="Code\App\STHitRanges.cs" />
    <Compile Include="Code\App\CIMEHook.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Code\Score,Song\EnumConverter.cs" />
    <Compile Include="Code\Score,Song\CChip.cs" />
    <Compile Include="Code\Score,Song\CProgress.cs" />
    <Compile Include="Code\Score,Song\EChannel.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectBackgroundAVI.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActTextBox.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CSongSearch.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfProgressBar.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarBonus.cs" />
    <Compile Include="Code\Stage\CDTXVmode.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="DTXManiaNX.exe.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Code\Item\CItemBase.cs" />
    <Compile Include="Code\Item\CItemInteger.cs" />
    <Compile Include="Code\Item\CItemList.cs" />
    <Compile Include="Code\Item\CItemThreeState.cs" />
    <Compile Include="Code\Item\CItemToggle.cs" />
    <Compile Include="Code\Score,Song\CBoxDef.cs" />
    <Compile Include="Code\Score,Song\CDTX.cs" />
    <Compile Include="Code\Score,Song\CScoreIni.cs" />
    <Compile Include="Code\Score,Song\CSetDef.cs" />
    <Compile Include="Code\Stage\02.Title\CEnumSongs.cs" />
    <Compile Include="Code\Score,Song\CSongManager.cs" />
    <Compile Include="Code\Score,Song\CScore.cs" />
    <Compile Include="Code\Score,Song\CSongListNode.cs" />
    <Compile Include="Code\Stage\01.Startup\CStageStartup.cs" />
    <Compile Include="Code\Stage\02.Title\CActEnumSongs.cs" />
    <Compile Include="Code\Stage\02.Title\CStageTitle.cs" />
    <Compile Include="Code\Stage\03.Option\CStageOption.cs" />
    <Compile Include="Code\Stage\04.Config\CActConfigKeyAssign.cs" />
    <Compile Include="Code\Stage\04.Config\CActConfigList.cs" />
    <Compile Include="Code\Stage\04.Config\CStageConfig.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectArtistComment.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectInformation.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectPopupMenu.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectPreimagePanel.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectPresound.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectQuickConfig.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectShowCurrentPosition.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectStatusPanel.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectSongList.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSelectPerfHistoryPanel.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CActSortSongs.cs" />
    <Compile Include="Code\Stage\05.SongSelection\CStageSongSelection.cs" />
    <Compile Include="Code\Stage\06.SongLoading\CStageSongLoading.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfBGA.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonCombo.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonDanger.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonRGB.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonWailingBonus.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonGauge.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfScrollSpeed.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonScore.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfStageClear.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonStatusPanel.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfChipFireGB.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfPanelString.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonLaneFlushGB.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfCommonJudgementString.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerformanceInformation.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfSkillMeter.cs" />
    <Compile Include="Code\Stage\07.Performance\CStagePerfCommonScreen.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarDanger.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarRGB.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarWailingBonus.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarGauge.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarCombo.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarScore.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarStatusPanel.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarChipFire.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarLaneFlushGB.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CActPerfGuitarJudgementString.cs" />
    <Compile Include="Code\Stage\07.Performance\GuitarScreen\CStagePerfGuitarScreen.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfStageFailure.cs" />
    <Compile Include="Code\Stage\07.Performance\CActPerfAVI.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsDanger.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsRGB.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsWailingBonus.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsGauge.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsComboDGB.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsScore.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsStatusPanel.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsChipFireD.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsChipFireGB.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsPad.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsFillingEffect.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsLaneFlushD.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsLaneFlushGB.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CActPerfDrumsJudgementString.cs" />
    <Compile Include="Code\Stage\07.Performance\DrumsScreen\CStagePerfDrumsScreen.cs" />
    <Compile Include="Code\Stage\08.Result\CActResultImage.cs" />
    <Compile Include="Code\Stage\08.Result\CActResultParameterPanel.cs" />
    <Compile Include="Code\Stage\08.Result\CActResultRank.cs" />
    <Compile Include="Code\Stage\08.Result\CActResultSongBar.cs" />
    <Compile Include="Code\Stage\08.Result\CStageResult.cs" />
    <Compile Include="Code\Stage\09.End\CStageEnd.cs" />
    <Compile Include="Code\Stage\10.ChangeSkin\CStageChangeSkin.cs" />
    <Compile Include="Code\Stage\CActDFPFont.cs" />
    <Compile Include="Code\Stage\CActFIFOBlack.cs" />
    <Compile Include="Code\Stage\CActFIFOWhite.cs" />
    <Compile Include="Code\Stage\CActLVLNFont.cs" />
    <Compile Include="Code\Stage\CActOptionPanel.cs" />
    <Compile Include="Code\Stage\CStage.cs" />
    <Compile Include="Code\Stage\CActFIFOWhiteClear.cs" />
    <Compile Include="Code\Stage\CActFIFOBlackStart.cs" />
    <Compile Include="Code\Plugin\CPluginHost.cs" />
    <Compile Include="Code\Plugin\IPluginActivity.cs" />
    <Compile Include="Code\Plugin\IPluginHost.cs" />
    <Compile Include="Code\App\CActFlushGPU.cs" />
    <Compile Include="Code\App\CConfigIni.cs" />
    <Compile Include="Code\App\CDTXMania.cs" />
    <Compile Include="Code\App\CDTXVersion.cs" />
    <Compile Include="Code\App\CPad.cs" />
    <Compile Include="Code\App\CPrivateFastFont.cs" />
    <Compile Include="Code\App\CPrivateFont.cs" />
    <Compile Include="Code\App\CSkin.cs" />
    <Compile Include="Code\App\CConstants.cs" />
    <Compile Include="Code\App\CCharacterConsole.cs" />
    <Compile Include="Code\App\Folder.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Code\App\Program.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="dtx.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\FDK\FDK.csproj">
      <Project>{bcd40908-f3e2-4707-bfaa-1dd99df6357d}</Project>
      <Name>FDK</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- Copy NuGet DLLs to output\dll\ -->
  <ItemDefinitionGroup>
    <ReferenceCopyLocalPaths>
      <DestinationSubDirectory>dll\</DestinationSubDirectory>
    </ReferenceCopyLocalPaths>
  </ItemDefinitionGroup>
</Project>