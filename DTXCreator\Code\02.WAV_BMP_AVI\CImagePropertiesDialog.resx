<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="textBoxラベル.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 37</value>
  </data>
  <data name="textBoxラベル.Size" type="System.Drawing.Size, System.Drawing">
    <value>294, 19</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBoxラベル.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="textBoxラベル.ToolTip" xml:space="preserve">
    <value>You can any label name to this BMP</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Name" xml:space="preserve">
    <value>textBoxラベル</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxラベル.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelラベル.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelラベル.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 40</value>
  </data>
  <data name="labelラベル.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 12</value>
  </data>
  <data name="labelラベル.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelラベル.Text" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="labelラベル.ToolTip" xml:space="preserve">
    <value>You can any label name to this BMP</value>
  </data>
  <data name="&gt;&gt;labelラベル.Name" xml:space="preserve">
    <value>labelラベル</value>
  </data>
  <data name="&gt;&gt;labelラベル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelラベル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelラベル.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textBoxファイル.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 62</value>
  </data>
  <data name="textBoxファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="textBoxファイル.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="textBoxファイル.ToolTip" xml:space="preserve">
    <value>Image file of this BMP</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Name" xml:space="preserve">
    <value>textBoxファイル</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxファイル.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelファイル.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelファイル.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 65</value>
  </data>
  <data name="labelファイル.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 12</value>
  </data>
  <data name="labelファイル.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="labelファイル.Text" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="labelファイル.ToolTip" xml:space="preserve">
    <value>Image file of this BMP</value>
  </data>
  <data name="&gt;&gt;labelファイル.Name" xml:space="preserve">
    <value>labelファイル</value>
  </data>
  <data name="&gt;&gt;labelファイル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelファイル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelファイル.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="button参照.Location" type="System.Drawing.Point, System.Drawing">
    <value>292, 58</value>
  </data>
  <data name="button参照.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 23</value>
  </data>
  <data name="button参照.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="button参照.Text" xml:space="preserve">
    <value>&amp;Browse...</value>
  </data>
  <data name="button参照.ToolTip" xml:space="preserve">
    <value>Select image file</value>
  </data>
  <data name="&gt;&gt;button参照.Name" xml:space="preserve">
    <value>button参照</value>
  </data>
  <data name="&gt;&gt;button参照.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button参照.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button参照.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="button背景色.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 10</value>
  </data>
  <data name="button背景色.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 23</value>
  </data>
  <data name="button背景色.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="button背景色.Text" xml:space="preserve">
    <value>BGcol.</value>
  </data>
  <data name="button背景色.ToolTip" xml:space="preserve">
    <value>Background color of this BMP</value>
  </data>
  <data name="&gt;&gt;button背景色.Name" xml:space="preserve">
    <value>button背景色</value>
  </data>
  <data name="&gt;&gt;button背景色.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button背景色.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button背景色.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="button文字色.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 10</value>
  </data>
  <data name="button文字色.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 23</value>
  </data>
  <data name="button文字色.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="button文字色.Text" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="button文字色.ToolTip" xml:space="preserve">
    <value>Text color of this BMP</value>
  </data>
  <data name="&gt;&gt;button文字色.Name" xml:space="preserve">
    <value>button文字色</value>
  </data>
  <data name="&gt;&gt;button文字色.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button文字色.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button文字色.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="button標準色に戻す.Location" type="System.Drawing.Point, System.Drawing">
    <value>249, 10</value>
  </data>
  <data name="button標準色に戻す.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 23</value>
  </data>
  <data name="button標準色に戻す.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="button標準色に戻す.Text" xml:space="preserve">
    <value>Reset colors</value>
  </data>
  <data name="button標準色に戻す.ToolTip" xml:space="preserve">
    <value>Reset to default colors</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Name" xml:space="preserve">
    <value>button標準色に戻す</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button標準色に戻す.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxBMPTEX.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxBMPTEX.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 87</value>
  </data>
  <data name="checkBoxBMPTEX.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 16</value>
  </data>
  <data name="checkBoxBMPTEX.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="checkBoxBMPTEX.Text" xml:space="preserve">
    <value>&amp;Texture</value>
  </data>
  <data name="checkBoxBMPTEX.ToolTip" xml:space="preserve">
    <value>If this is checked, this BMP is created as Direct3D Texture.</value>
  </data>
  <data name="&gt;&gt;checkBoxBMPTEX.Name" xml:space="preserve">
    <value>checkBoxBMPTEX</value>
  </data>
  <data name="&gt;&gt;checkBoxBMPTEX.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxBMPTEX.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxBMPTEX.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxBMP番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 12</value>
  </data>
  <data name="textBoxBMP番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 19</value>
  </data>
  <data name="textBoxBMP番号.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBoxBMP番号.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;textBoxBMP番号.Name" xml:space="preserve">
    <value>textBoxBMP番号</value>
  </data>
  <data name="&gt;&gt;textBoxBMP番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxBMP番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxBMP番号.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="labelBMP番号.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelBMP番号.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 15</value>
  </data>
  <data name="labelBMP番号.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 12</value>
  </data>
  <data name="labelBMP番号.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelBMP番号.Text" xml:space="preserve">
    <value>BMP No.</value>
  </data>
  <data name="&gt;&gt;labelBMP番号.Name" xml:space="preserve">
    <value>labelBMP番号</value>
  </data>
  <data name="&gt;&gt;labelBMP番号.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelBMP番号.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelBMP番号.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 99</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonキャンセル.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 99</value>
  </data>
  <data name="buttonキャンセル.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonキャンセル.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonキャンセル.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Name" xml:space="preserve">
    <value>buttonキャンセル</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonキャンセル.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>378, 136</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Image property</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>C画像プロパティダイアログ</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>